#!/usr/bin/env python3
"""
GGL Watermark Evaluation Script
Adopts the standard MarkLLM evaluation pattern for GGL watermark.
"""

import torch
from evaluation.dataset import C4Dataset
from watermark.auto_watermark import AutoWatermark
from utils.transformers_config import TransformersConfig
from transformers import AutoTokenizer
from evaluation.tools.text_editor import Truncate<PERSON>romptTextEditor, WordDeletion
from evaluation.tools.success_rate_calculator import DynamicThresholdSuccessRateCalculator
from evaluation.pipelines.detection import WatermarkedTextDetectionPipeline, UnWatermarkedTextDetectionPipeline, DetectionPipelineReturnType

def test_ggl_generation(my_watermark, sample_prompt):
    """Test GGL generation with a sample prompt to debug issues."""
    print(f"\nTesting GGL generation with prompt: '{sample_prompt[:50]}...'")
    
    try:
        # Test watermarked generation
        watermarked_text = my_watermark.generate_watermarked_text(sample_prompt)
        print(f"✓ Watermarked text generated ({len(watermarked_text)} chars)")
        print(f"  Sample: '{watermarked_text[:100]}...'")
        
        # Test detection
        detection_result = my_watermark.detect_watermark(watermarked_text, prompt=sample_prompt)
        print(f"✓ Detection result: {detection_result}")
        
        # Test unwatermarked generation
        unwatermarked_text = my_watermark.generate_unwatermarked_text(sample_prompt)
        print(f"✓ Unwatermarked text generated ({len(unwatermarked_text)} chars)")
        print(f"  Sample: '{unwatermarked_text[:100]}...'")
        
        return True
        
    except Exception as e:
        print(f"❌ Generation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_ggl_evaluation():
    """Run evaluation for GGL watermark using MarkLLM evaluation framework."""
    
    print("GGL Watermark Evaluation")
    print("=" * 40)
    
    # Load dataset
    print("Loading C4 dataset...")
    my_dataset = C4Dataset('dataset/c4/processed_c4.json')
    print("✓ Dataset loaded successfully")

    # Device
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"Using device: {device}")

    # Transformers config - MODIFIED for GGL
    print("Setting up transformers config for GGL...")
    tokenizer = AutoTokenizer.from_pretrained('GSAI-ML/LLaDA-8B-Instruct', trust_remote_code=True)
    
    transformers_config = TransformersConfig(
        model=None,  # GGL loads its own LLaDA model
        tokenizer=tokenizer,
        vocab_size=tokenizer.vocab_size,
        device=device,
        max_new_tokens=200,
        do_sample=True,
        min_length=50,  # Adjusted for GGL
        no_repeat_ngram_size=4
    )
    print("✓ Transformers config ready")

    # Load watermark algorithm - CHANGED to GGL
    print("Loading GGL watermark...")
    my_watermark = AutoWatermark.load('GGL', 
                                      algorithm_config='config/GGL.json',
                                      transformers_config=transformers_config)
    print("✓ GGL watermark loaded")

    # Test generation first
    sample_prompt = my_dataset.prompts[0]  # Get first prompt
    if not test_ggl_generation(my_watermark, sample_prompt):
        print("❌ Generation test failed. Stopping evaluation.")
        return None

    # Init pipelines with reduced complexity for initial testing
    print("Initializing evaluation pipelines...")
    
    # Reduce complexity for initial testing
    pipeline1 = WatermarkedTextDetectionPipeline(
        dataset=my_dataset, 
        text_editor_list=[TruncatePromptTextEditor()],  # Remove WordDeletion for now
        show_progress=True, 
        return_type=DetectionPipelineReturnType.SCORES
    ) 

    pipeline2 = UnWatermarkedTextDetectionPipeline(
        dataset=my_dataset, 
        text_editor_list=[],
        show_progress=True,
        return_type=DetectionPipelineReturnType.SCORES
    )
    print("✓ Pipelines initialized")

    # Evaluate with error handling
    try:
        print("\nRunning watermarked text detection pipeline...")
        watermarked_scores = pipeline1.evaluate(my_watermark)
        print(f"✓ Watermarked pipeline completed. Got {len(watermarked_scores)} scores")
        
        print("Running unwatermarked text detection pipeline...")
        unwatermarked_scores = pipeline2.evaluate(my_watermark)
        print(f"✓ Unwatermarked pipeline completed. Got {len(unwatermarked_scores)} scores")

        # Calculate success rates
        print("\nCalculating success rates...")
        calculator = DynamicThresholdSuccessRateCalculator(labels=['TPR', 'F1'], rule='best')
        results = calculator.calculate(watermarked_scores, unwatermarked_scores)
        
        print("\n" + "=" * 40)
        print("GGL Evaluation Results:")
        print("=" * 40)
        print(results)
        print("\n✅ GGL evaluation completed!")
        
        return results
        
    except Exception as e:
        print(f"\n❌ Pipeline evaluation failed: {e}")
        import traceback
        traceback.print_exc()
        
        # Try a simpler evaluation approach
        print("\nTrying simplified evaluation...")
        try:
            # Test with a few samples manually
            sample_prompts = my_dataset.prompts[:5]  # Get 5 prompts
            watermarked_scores = []
            unwatermarked_scores = []
            
            for i, prompt in enumerate(sample_prompts):
                print(f"Processing sample {i+1}/5...")
                
                # Generate texts
                w_text = my_watermark.generate_watermarked_text(prompt)
                u_text = my_watermark.generate_unwatermarked_text(prompt)
                
                # Detect
                w_result = my_watermark.detect_watermark(w_text, prompt=prompt)
                u_result = my_watermark.detect_watermark(u_text, prompt=prompt)
                
                watermarked_scores.append(w_result['score'])
                unwatermarked_scores.append(u_result['score'])
                
                print(f"  W-score: {w_result['score']:.3f}, U-score: {u_result['score']:.3f}")
            
            # Simple statistics
            import numpy as np
            w_mean = np.mean(watermarked_scores)
            u_mean = np.mean(unwatermarked_scores)
            separation = w_mean - u_mean
            
            print(f"\nSimple Evaluation Results:")
            print(f"Watermarked mean score: {w_mean:.3f}")
            print(f"Unwatermarked mean score: {u_mean:.3f}")
            print(f"Separation: {separation:.3f}")
            
            return {
                'watermarked_mean': w_mean,
                'unwatermarked_mean': u_mean,
                'separation': separation
            }
            
        except Exception as e2:
            print(f"❌ Simplified evaluation also failed: {e2}")
            import traceback
            traceback.print_exc()
            return None

if __name__ == "__main__":
    try:
        results = run_ggl_evaluation()
        if results:
            print(f"\n🎉 Evaluation successful!")
        else:
            print(f"\n❌ Evaluation failed.")
    except Exception as e:
        print(f"\n❌ Error during evaluation: {e}")
        import traceback
        traceback.print_exc()
