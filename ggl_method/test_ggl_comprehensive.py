#!/usr/bin/env python3
"""
Comprehensive test demonstrating complete GGL integration into MarkLLM framework.
This test shows all the ways GGL can be used within the framework.
"""

import sys
import torch
sys.path.insert(0, '/mnt/newhome/kasra/MarkLLM')

from watermark.auto_watermark import AutoWatermark
from watermark.auto_config import AutoConfig
from watermark.ggl.ggl import GGL, GGLConfig
from utils.transformers_config import TransformersConfig
from transformers import AutoTokenizer

def comprehensive_integration_test():
    """Test all integration methods for GGL watermark."""
    
    print("GGL MarkLLM Integration - Comprehensive Test")
    print("=" * 50)
    
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    tokenizer = AutoTokenizer.from_pretrained("GSAI-ML/LLaDA-8B-Instruct", trust_remote_code=True)
    transformers_config = TransformersConfig(
        model=None,
        tokenizer=tokenizer,
        vocab_size=tokenizer.vocab_size,
        device=device
    )
    
    test_prompt = "Write about the role of AI in education:"
    
    try:
        # Method 1: Direct instantiation
        print("1. Testing Direct Instantiation...")
        ggl_direct = GGL("config/GGL.json", transformers_config)
        text1 = ggl_direct.generate_watermarked_text(test_prompt)
        result1 = ggl_direct.detect_watermark(text1, prompt=test_prompt)
        print(f"   Direct: Generated {len(text1)} chars, Score: {result1['score']:.3f}")
        
        # Method 2: AutoConfig + Direct
        print("\n2. Testing AutoConfig + Direct...")
        ggl_config = AutoConfig.load("GGL", transformers_config)
        ggl_auto_config = GGL(ggl_config)
        text2 = ggl_auto_config.generate_watermarked_text(test_prompt)
        result2 = ggl_auto_config.detect_watermark(text2, prompt=test_prompt)
        print(f"   AutoConfig: Generated {len(text2)} chars, Score: {result2['score']:.3f}")
        
        # Method 3: Full AutoWatermark
        print("\n3. Testing Full AutoWatermark...")
        ggl_auto = AutoWatermark.load("GGL", None, transformers_config)
        text3 = ggl_auto.generate_watermarked_text(test_prompt)
        result3 = ggl_auto.detect_watermark(text3, prompt=test_prompt)
        print(f"   AutoWatermark: Generated {len(text3)} chars, Score: {result3['score']:.3f}")
        
        # Method 4: Visualization test
        print("\n4. Testing Visualization...")
        viz_data = ggl_auto.get_data_for_visualization(text3, prompt=test_prompt)
        green_count = sum(viz_data.highlight_values)
        total_tokens = len(viz_data.highlight_values)
        print(f"   Visualization: {green_count}/{total_tokens} green tokens ({green_count/total_tokens:.1%})")
        
        # Summary
        print("\n" + "=" * 50)
        print("✓ All integration methods working successfully!")
        print("\nGGL Watermark Integration Summary:")
        print("✓ Added to config/GGL.json")
        print("✓ Registered in watermark/auto_config.py")
        print("✓ Registered in watermark/auto_watermark.py")
        print("✓ Direct instantiation works")
        print("✓ AutoConfig loading works")
        print("✓ AutoWatermark loading works")
        print("✓ Generation, detection, and visualization all functional")
        print("\n🎉 GGL is fully integrated into MarkLLM framework!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = comprehensive_integration_test()
    sys.exit(0 if success else 1)
