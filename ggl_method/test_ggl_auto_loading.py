#!/usr/bin/env python3
"""
Test script to verify GGL watermark can be loaded automatically via MarkLLM's AutoWatermark system.
"""

import sys
import torch
sys.path.insert(0, '/mnt/newhome/kasra/MarkLLM')

from watermark.auto_watermark import AutoWatermark
from utils.transformers_config import TransformersConfig
from transformers import AutoTokenizer

def test_auto_loading():
    """Test automatic loading of GGL watermark via AutoWatermark."""
    
    print("Testing GGL AutoWatermark Loading")
    print("=" * 40)
    
    try:
        # Setup tokenizer and transformers config
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        tokenizer = AutoTokenizer.from_pretrained("GSAI-ML/LLaDA-8B-Instruct", trust_remote_code=True)
        
        transformers_config = TransformersConfig(
            model=None,  # GGL will load its own model
            tokenizer=tokenizer,
            vocab_size=tokenizer.vocab_size,
            device=device
        )
        
        print("1. Testing AutoWatermark.load() for GGL...")
        
        # Load GGL watermark automatically
        watermark = AutoWatermark.load(
            algorithm_name="GGL",
            algorithm_config=None,  # Will use default config/GGL.json
            transformers_config=transformers_config
        )
        
        print("   ✓ GGL loaded successfully via AutoWatermark")
        print(f"   ✓ Watermark type: {type(watermark)}")
        print(f"   ✓ Algorithm name: {watermark.config.algorithm_name}")
        
        # Test generation
        print("\n2. Testing generation via AutoWatermark...")
        test_prompt = "Describe the future of AI:"
        generated_text = watermark.generate_watermarked_text(test_prompt)
        print(f"   Generated: {generated_text[:80]}...")
        
        # Test detection
        print("\n3. Testing detection via AutoWatermark...")
        detection_result = watermark.detect_watermark(generated_text, prompt=test_prompt)
        print(f"   Detection result: {detection_result}")
        
        print("\n" + "=" * 40)
        print("✓ AutoWatermark integration successful!")
        print("✓ GGL is now fully integrated into MarkLLM framework")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_auto_loading()
    sys.exit(0 if success else 1)
