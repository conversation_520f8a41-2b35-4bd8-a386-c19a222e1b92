#!/usr/bin/env python3
"""
Demonstration script showing how to use the GGL (Guided Green List) watermark
in the MarkLLM framework.
"""

import sys
import torch
sys.path.insert(0, '/mnt/newhome/kasra/MarkLLM')

from watermark.ggl.ggl import GGL
from utils.transformers_config import TransformersConfig
from transformers import AutoTokenizer

def demo_ggl_watermark():
    """Demonstrate the GGL watermark functionality."""
    
    print("GGL (Guided Green List) Watermark Demonstration")
    print("=" * 50)
    
    # Configuration
    config_path = "/mnt/newhome/kasra/MarkLLM/config/GGL.json"
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Setup tokenizer and transformers config
    tokenizer = AutoTokenizer.from_pretrained("GSAI-ML/LLaDA-8B-Instruct", trust_remote_code=True)
    transformers_config = TransformersConfig(
        model=None,  # GGL will load its own LLaDA model
        tokenizer=tokenizer,
        vocab_size=tokenizer.vocab_size,
        device=device
    )
    
    # Initialize the GGL watermark
    print("Initializing GGL watermark...")
    ggl = GGL(config_path, transformers_config)
    print("✓ Initialization complete\n")
    
    # Test prompts
    test_prompts = [
        "Explain artificial intelligence in simple terms:",
        "Write a short poem about technology:",
        "Describe the future of robotics:"
    ]
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"Example {i}:")
        print(f"Prompt: {prompt}")
        
        # Generate watermarked text
        watermarked_text = ggl.generate_watermarked_text(prompt)
        print(f"Generated: {watermarked_text[:100]}...")
        
        # Detect watermark
        detection_result = ggl.detect_watermark(watermarked_text, prompt=prompt)
        print(f"Detection: {detection_result}")
        print("-" * 50)
    
    print("\n✓ Demonstration complete!")
    print("\nKey Features of GGL Watermark:")
    print("• Uses Guided Green List for LLaDA model watermarking")
    print("• Compatible with MarkLLM framework")
    print("• Supports both generation and detection")
    print("• Provides visualization data for analysis")

if __name__ == "__main__":
    demo_ggl_watermark()
