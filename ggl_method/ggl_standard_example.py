#!/usr/bin/env python3
"""
Example showing how to use GGL watermark with the standard MarkLLM pattern.
This is the corrected version for your use case.
"""

import torch
from watermark.auto_watermark import AutoWatermark
from utils.transformers_config import TransformersConfig
from transformers import AutoTokenizer

# Device
device = "cuda" if torch.cuda.is_available() else "cpu"

# For GGL, we need the LLaDA tokenizer (not OPT)
# The LLaDA model will be loaded automatically by GGL
tokenizer = AutoTokenizer.from_pretrained('GSAI-ML/LLaDA-8B-Instruct', trust_remote_code=True)

# Transformers config
transformers_config = TransformersConfig(
    model=None,  # GGL will load its own LLaDA model - don't use OPT
    tokenizer=tokenizer,
    vocab_size=tokenizer.vocab_size,  # Use LLaDA vocab size
    device=device,
    max_new_tokens=200,
    min_length=50,  # Adjusted for reasonable output
    do_sample=True,
    no_repeat_ngram_size=4
)
  
# Load watermark algorithm - change 'KGW' to 'GGL'
myWatermark = AutoWatermark.load('GGL', 
                                 algorithm_config='config/GGL.json',
                                 transformers_config=transformers_config)

# Prompt
prompt = 'Good Morning. Tell me about artificial intelligence.'

print("=" * 50)
print("GGL Watermark Example")
print("=" * 50)
print(f"Prompt: {prompt}")
print()

# Generate watermarked text
print("Generating watermarked text...")
watermarked_text = myWatermark.generate_watermarked_text(prompt)
print(f"Watermarked: {watermarked_text}")
print()

# Detect watermark - IMPORTANT: GGL needs the original prompt
print("Detecting watermark in watermarked text...")
detect_result = myWatermark.detect_watermark(watermarked_text, prompt=prompt)
print(f"Detection result: {detect_result}")
print()

# Generate unwatermarked text
print("Generating unwatermarked text...")
unwatermarked_text = myWatermark.generate_unwatermarked_text(prompt)
print(f"Unwatermarked: {unwatermarked_text}")
print()

# Detect on unwatermarked text
print("Detecting watermark in unwatermarked text...")
detect_result_unwm = myWatermark.detect_watermark(unwatermarked_text, prompt=prompt)
print(f"Detection result: {detect_result_unwm}")
print()

print("=" * 50)
print("Key Differences for GGL:")
print("• Use 'GSAI-ML/LLaDA-8B-Instruct' tokenizer")
print("• Set model=None in TransformersConfig")
print("• Use algorithm name 'GGL'")
print("• Pass prompt= parameter to detect_watermark()")
print("=" * 50)
