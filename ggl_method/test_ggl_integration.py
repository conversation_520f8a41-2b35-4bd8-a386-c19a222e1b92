#!/usr/bin/env python3
"""
Test script for GGL (Guided Green List) watermark implementation in MarkLLM.
This script tests the basic functionality of our GGL watermark integration.
"""

import sys
import os
import torch

# Add the MarkLLM directory to the path
sys.path.insert(0, '/mnt/newhome/kasra/MarkLLM')

from watermark.ggl.ggl import GGL, GGLConfig
from utils.transformers_config import TransformersConfig
from transformers import AutoTokenizer

def test_ggl_implementation():
    """Test the GGL implementation with a simple example."""
    
    print("Testing GGL (Guided Green List) Watermark Implementation")
    print("=" * 60)
    
    # Test configuration loading
    try:
        print("1. Testing configuration loading...")
        config_path = "/mnt/newhome/kasra/MarkLLM/config/GGL.json"
        
        # Create a dummy transformers config (we'll override the model anyway)
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"   Using device: {device}")
        
        # Load tokenizer (we'll use the one from the LLaDA model)
        tokenizer = AutoTokenizer.from_pretrained("GSAI-ML/LLaDA-8B-Instruct", trust_remote_code=True)
        
        # Create transformers config
        transformers_config = TransformersConfig(
            model=None,  # Will be overridden by GGL
            tokenizer=tokenizer,
            vocab_size=tokenizer.vocab_size,
            device=device,
            gen_kwargs={"max_length": 100, "do_sample": True, "temperature": 0.8}
        )
        
        print("   ✓ Configuration setup complete")
        
        # Initialize GGL
        print("\n2. Initializing GGL watermark...")
        ggl = GGL(config_path, transformers_config)
        print("   ✓ GGL initialized successfully")
        
        # Test detection without generation (should work)
        print("\n3. Testing watermark detection...")
        test_prompt = "Write a short story about artificial intelligence."
        test_text = "In the near future, artificial intelligence became so advanced that it could write poetry."
        
        detection_result = ggl.detect_watermark(test_text, prompt=test_prompt)
        print(f"   Detection result: {detection_result}")
        print("   ✓ Detection test complete")
        
        # Test visualization data
        print("\n4. Testing visualization data generation...")
        viz_data = ggl.get_data_for_visualization(test_text, prompt=test_prompt)
        print(f"   Tokens: {viz_data.decoded_tokens[:5]}...")  # Show first 5 tokens
        print(f"   Highlights: {viz_data.highlight_values[:5]}...")  # Show first 5 highlights
        print("   ✓ Visualization test complete")
        
        print("\n" + "=" * 60)
        print("✓ All basic tests passed! GGL implementation is working.")
        print("\nNote: Full generation testing requires GPU and the LLaDA model to be loaded.")
        print("The implementation is ready for integration into the MarkLLM framework.")
        
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = test_ggl_implementation()
    sys.exit(0 if success else 1)
