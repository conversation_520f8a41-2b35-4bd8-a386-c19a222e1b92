#!/usr/bin/env python3
"""
GGL Quality Analysis Script - Evaluates text quality using perplexity metrics.
Adapted from MarkLLM evaluation framework for GGL watermark.
"""

import torch
import sys
sys.path.insert(0, '/mnt/newhome/kasra/MarkLLM')

from evaluation.dataset import C4Dataset
from watermark.auto_watermark import AutoWatermark
from utils.transformers_config import TransformersConfig
from transformers import AutoModelForCausalLM, AutoTokenizer
from evaluation.tools.text_editor import TruncatePromptTextEditor
from evaluation.tools.text_quality_analyzer import PPLCalculator
from evaluation.pipelines.quality_analysis import DirectTextQualityAnalysisPipeline, QualityPipelineReturnType

def run_ggl_quality_analysis():
    """Run quality analysis evaluation for GGL watermark."""
    
    print("GGL Quality Analysis Evaluation")
    print("=" * 40)
    
    try:
        # Load dataset
        print("Loading C4 dataset...")
        my_dataset = C4Dataset('dataset/c4/processed_c4.json')
        print("✓ C4 dataset loaded")

        # Device
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"✓ Using device: {device}")

        # For GGL, we need LLaDA tokenizer and model setup
        print("Setting up GGL tokenizer...")
        ggl_tokenizer = AutoTokenizer.from_pretrained('GSAI-ML/LLaDA-8B-Instruct', trust_remote_code=True)
        
        # Transformers config for GGL
        transformers_config = TransformersConfig(
            model=None,  # GGL loads its own LLaDA model
            tokenizer=ggl_tokenizer,
            vocab_size=ggl_tokenizer.vocab_size,
            device=device,
            max_new_tokens=200,
            min_length=50,  # Adjusted for GGL
            do_sample=True,
            no_repeat_ngram_size=4
        )
        print("✓ Transformers config ready")

        # Load GGL watermark algorithm
        print("Loading GGL watermark...")
        my_watermark = AutoWatermark.load('GGL', 
                                          algorithm_config='config/GGL.json',
                                          transformers_config=transformers_config)
        print("✓ GGL watermark loaded")

        # Setup quality analyzer - using a smaller model for perplexity calculation
        print("Setting up quality analyzer...")
        try:
            # Try to use GPT-2 for perplexity calculation (smaller and more accessible)
            ppl_model = AutoModelForCausalLM.from_pretrained('gpt2').to(device)
            ppl_tokenizer = AutoTokenizer.from_pretrained('gpt2')
            if ppl_tokenizer.pad_token is None:
                ppl_tokenizer.pad_token = ppl_tokenizer.eos_token
            print("✓ Using GPT-2 for perplexity calculation")
        except Exception as e:
            print(f"Could not load GPT-2, trying distilgpt2: {e}")
            # Fallback to smaller model
            ppl_model = AutoModelForCausalLM.from_pretrained('distilgpt2').to(device)
            ppl_tokenizer = AutoTokenizer.from_pretrained('distilgpt2')
            if ppl_tokenizer.pad_token is None:
                ppl_tokenizer.pad_token = ppl_tokenizer.eos_token
            print("✓ Using DistilGPT-2 for perplexity calculation")

        # Init quality analysis pipeline
        print("Initializing quality pipeline...")
        quality_pipeline = DirectTextQualityAnalysisPipeline(
            dataset=my_dataset, 
            watermarked_text_editor_list=[TruncatePromptTextEditor()],
            unwatermarked_text_editor_list=[],                                             
            analyzers=[PPLCalculator(
                model=ppl_model,
                tokenizer=ppl_tokenizer,
                device=device
            )],
            unwatermarked_text_source='natural', 
            show_progress=True, 
            return_type=QualityPipelineReturnType.MEAN_SCORES
        )
        print("✓ Quality pipeline initialized")

        # Run evaluation with error handling
        print("\nStarting quality evaluation...")
        print("This may take several minutes...")
        
        # Run full evaluation
        results = quality_pipeline.evaluate(my_watermark)
        
        print("\n" + "=" * 40)
        print("GGL Quality Analysis Results:")
        print("=" * 40)
        
        if isinstance(results, dict):
            for metric, value in results.items():
                print(f"{metric}: {value:.4f}")
        else:
            print(f"Quality Score: {results}")
        
        print("=" * 40)
        print("✓ Quality analysis completed successfully!")
        
        return results
        
    except Exception as e:
        print(f"\n❌ Error during quality analysis: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    results = run_ggl_quality_analysis()
    if results is None:
        sys.exit(1)
    else:
        print(f"\nFinal Results: {results}")
        sys.exit(0)
