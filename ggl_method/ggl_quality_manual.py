#!/usr/bin/env python3
"""
Fixed GGL Quality Analysis - handles tokenization mismatches properly.
"""

import torch
import sys
sys.path.insert(0, '/mnt/newhome/kasra/MarkLLM')

from evaluation.dataset import C4Dataset
from watermark.auto_watermark import AutoWatermark
from utils.transformers_config import TransformersConfig
from transformers import AutoModelForCausalLM, AutoTokenizer

def test_ggl_quality_manual():
    """Manual quality test for GGL that handles tokenization issues."""
    
    print("GGL Manual Quality Analysis")
    print("=" * 30)
    
    # Setup
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # Load dataset
    print("Loading dataset...")
    my_dataset = C4Dataset('dataset/c4/processed_c4.json')
    sample_data = list(my_dataset)[:5]  # Get 5 samples
    print(f"✓ Got {len(sample_data)} samples")
    
    # Setup GGL
    print("Setting up GGL...")
    ggl_tokenizer = AutoTokenizer.from_pretrained('GSAI-ML/LLaDA-8B-Instruct', trust_remote_code=True)
    transformers_config = TransformersConfig(
        model=None,
        tokenizer=ggl_tokenizer,
        vocab_size=ggl_tokenizer.vocab_size,
        device=device,
        max_new_tokens=200,
        min_length=50,
        do_sample=True,
        no_repeat_ngram_size=4
    )
    
    my_watermark = AutoWatermark.load('GGL', 'config/GGL.json', transformers_config)
    print("✓ GGL loaded")
    
    # Setup perplexity calculator with same tokenizer as GGL
    print("Setting up perplexity calculator...")
    # Use the same LLaDA model for perplexity to avoid tokenization issues
    ppl_model = my_watermark.config.generation_model
    ppl_tokenizer = ggl_tokenizer
    print("✓ Using LLaDA model for perplexity calculation")
    
    # Analyze samples
    watermarked_ppls = []
    unwatermarked_ppls = []
    
    print("\nAnalyzing samples...")
    for i, sample in enumerate(sample_data):
        print(f"Sample {i+1}/5...")
        prompt = sample['text']
        
        try:
            # Generate texts
            w_text = my_watermark.generate_watermarked_text(prompt)
            u_text = my_watermark.generate_unwatermarked_text(prompt)
            
            print(f"  W: {len(w_text)} chars, U: {len(u_text)} chars")
            
            # Calculate perplexity manually using the same model
            def calculate_ppl(text):
                if not text.strip():
                    return float('inf')
                
                try:
                    # Format as chat message like in generation
                    m = [{"role": "assistant", "content": text}]
                    formatted_text = ppl_tokenizer.apply_chat_template(m, tokenize=False, add_generation_prompt=False)
                    
                    encoded = ppl_tokenizer(formatted_text, return_tensors="pt", add_special_tokens=False)["input_ids"][0].to(device)
                    
                    if len(encoded) <= 1:
                        return float('inf')
                    
                    with torch.no_grad():
                        logits = ppl_model(torch.unsqueeze(encoded, 0)).logits[0]
                        
                    criterion = torch.nn.CrossEntropyLoss()
                    loss = criterion(logits[:-1], encoded[1:])
                    ppl = torch.exp(loss).item()
                    
                    return ppl
                except Exception as e:
                    print(f"    PPL calculation failed: {e}")
                    return float('inf')
            
            w_ppl = calculate_ppl(w_text)
            u_ppl = calculate_ppl(u_text)
            
            print(f"  W PPL: {w_ppl:.2f}, U PPL: {u_ppl:.2f}")
            
            if w_ppl != float('inf'):
                watermarked_ppls.append(w_ppl)
            if u_ppl != float('inf'):
                unwatermarked_ppls.append(u_ppl)
                
        except Exception as e:
            print(f"  Error: {e}")
            continue
    
    # Results
    print("\n" + "=" * 30)
    print("Results:")
    if watermarked_ppls and unwatermarked_ppls:
        w_mean = sum(watermarked_ppls) / len(watermarked_ppls)
        u_mean = sum(unwatermarked_ppls) / len(unwatermarked_ppls)
        
        print(f"Watermarked PPL:   {w_mean:.3f} (n={len(watermarked_ppls)})")
        print(f"Unwatermarked PPL: {u_mean:.3f} (n={len(unwatermarked_ppls)})")
        print(f"Quality degradation: {w_mean - u_mean:.3f}")
        
        if w_mean < u_mean * 1.5:  # Less than 50% increase is good
            print("✅ Good quality preservation")
        else:
            print("⚠️  Some quality degradation detected")
    else:
        print("❌ Could not calculate perplexities")

if __name__ == "__main__":
    test_ggl_quality_manual()
