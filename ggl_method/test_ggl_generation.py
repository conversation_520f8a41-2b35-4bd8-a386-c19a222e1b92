#!/usr/bin/env python3
"""
Full test script for GGL watermark including text generation.
"""

import sys
import os
import torch

# Add the MarkLLM directory to the path
sys.path.insert(0, '/mnt/newhome/kasra/MarkLLM')

from watermark.ggl.ggl import GGL, GGLConfig
from utils.transformers_config import TransformersConfig
from transformers import AutoTokenizer

def test_ggl_generation():
    """Test the GGL implementation with actual text generation."""
    
    print("Testing GGL Text Generation")
    print("=" * 40)
    
    try:
        # Setup
        config_path = "/mnt/newhome/kasra/MarkLLM/config/GGL.json"
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Load tokenizer
        tokenizer = AutoTokenizer.from_pretrained("GSAI-ML/LLaDA-8B-Instruct", trust_remote_code=True)
        
        # Create transformers config
        transformers_config = TransformersConfig(
            model=None,  # Will be overridden by GGL
            tokenizer=tokenizer,
            vocab_size=tokenizer.vocab_size,
            device=device,
            gen_kwargs={"max_length": 100, "do_sample": True, "temperature": 0.8}
        )
        
        # Initialize GGL
        print("Initializing GGL...")
        ggl = GGL(config_path, transformers_config)
        print("✓ GGL initialized")
        
        # Test generation
        test_prompt = "Write a brief story about a robot:"
        print(f"\nPrompt: {test_prompt}")
        print("Generating watermarked text...")
        
        generated_text = ggl.generate_watermarked_text(test_prompt)
        print(f"Generated: {generated_text}")
        
        # Test detection
        print("\nTesting detection...")
        detection_result = ggl.detect_watermark(generated_text, prompt=test_prompt)
        print(f"Detection result: {detection_result}")
        
        print("\n✓ Generation and detection test completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = test_ggl_generation()
    sys.exit(0 if success else 1)
