#!/usr/bin/env python3
"""
GGL Threshold Optimization Script
Uses DynamicThresholdSuccessRateCalculator to find the best detection threshold for GGL watermark.
"""

import sys
sys.path.insert(0, '/mnt/newhome/kasra/MarkLLM')

import torch
from evaluation.dataset import C4Dataset
from watermark.auto_watermark import AutoWatermark
from utils.transformers_config import TransformersConfig
from transformers import AutoTokenizer
from evaluation.tools.success_rate_calculator import DynamicThresholdSuccessRateCalculator

def find_ggl_best_threshold():
    """Find the best detection threshold for GGL watermark."""
    
    print("GGL Threshold Optimization")
    print("=" * 40)
    
    # Setup
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"Using device: {device}")
    
    # Load dataset
    print("Loading C4 dataset...")
    my_dataset = C4Dataset('dataset/c4/processed_c4.json')
    print("✓ Dataset loaded")
    
    # Setup GGL watermark
    print("Setting up GGL watermark...")
    tokenizer = AutoTokenizer.from_pretrained('GSAI-ML/LLaDA-8B-Instruct', trust_remote_code=True)
    transformers_config = TransformersConfig(
        model=None,  # GGL loads its own model
        tokenizer=tokenizer,
        vocab_size=tokenizer.vocab_size,
        device=device
    )
    
    print("Loading GGL watermark...")
    my_watermark = AutoWatermark.load('GGL', 'config/GGL.json', transformers_config)
    print("✓ GGL watermark loaded")
    
    # Generate evaluation data
    print(f"\nGenerating evaluation data...")
    num_samples = 20  # Start with smaller number for testing
    
    watermarked_scores = []
    unwatermarked_scores = []
    
    # Use the exact same pattern as ggl_evaluation.py
    prompts = my_dataset.prompts[:num_samples]  # Get prompts from dataset
    
    for i, prompt in enumerate(prompts):
        print(f"Processing sample {i+1}/{len(prompts)}...")
        
        try:
            # Generate watermarked text
            w_text = my_watermark.generate_watermarked_text(prompt)
            w_result = my_watermark.detect_watermark(w_text, prompt=prompt)
            watermarked_scores.append(w_result['score'])
            
            # Generate unwatermarked text  
            u_text = my_watermark.generate_unwatermarked_text(prompt)
            u_result = my_watermark.detect_watermark(u_text, prompt=prompt)
            unwatermarked_scores.append(u_result['score'])
            
            print(f"  W-score: {w_result['score']:.3f}, U-score: {u_result['score']:.3f}")
            
        except Exception as e:
            print(f"  ⚠️  Error processing sample {i+1}: {e}")
            continue
    
    print(f"\n✓ Generated {len(watermarked_scores)} sample pairs")
    
    # Find best threshold using different optimization strategies
    print("\n" + "=" * 40)
    print("Threshold Optimization Results")
    print("=" * 40)
    
    # Strategy 1: Best F1 Score
    print("\n1. Best F1 Score Optimization:")
    calculator_f1 = DynamicThresholdSuccessRateCalculator(
        labels=['TPR', 'TNR', 'FPR', 'FNR', 'P', 'R', 'F1', 'ACC'], 
        rule='best'
    )

    print(f"   Best F1 threshold: {calculator_f1.best_threshold:.4f}")
    
    results_f1 = calculator_f1.calculate(watermarked_scores, unwatermarked_scores)
    print(f"   Results: {results_f1}")
    
    # Strategy 2: Target FPR = 0.05 (5% false positive rate)
    print("\n2. Target FPR = 0.05 Optimization:")
    calculator_fpr = DynamicThresholdSuccessRateCalculator(
        labels=['TPR', 'TNR', 'FPR', 'FNR', 'P', 'R', 'F1', 'ACC'], 
        rule='target_fpr',
        target_fpr=0.05
    )
    
    results_fpr = calculator_fpr.calculate(watermarked_scores, unwatermarked_scores)
    print(f"   Results: {results_fpr}")
    
    # Strategy 3: Target FPR = 0.01 (1% false positive rate - more conservative)
    print("\n3. Target FPR = 0.01 Optimization (Conservative):")
    calculator_conservative = DynamicThresholdSuccessRateCalculator(
        labels=['TPR', 'TNR', 'FPR', 'FNR', 'P', 'R', 'F1', 'ACC'], 
        rule='target_fpr',
        target_fpr=0.01
    )
    
    results_conservative = calculator_conservative.calculate(watermarked_scores, unwatermarked_scores)
    print(f"   Results: {results_conservative}")
    
    # Data analysis
    print("\n" + "=" * 40)
    print("Score Distribution Analysis")
    print("=" * 40)
    
    import numpy as np
    
    w_mean = np.mean(watermarked_scores)
    w_std = np.std(watermarked_scores)
    u_mean = np.mean(unwatermarked_scores)
    u_std = np.std(unwatermarked_scores)
    
    print(f"Watermarked scores   - Mean: {w_mean:.4f}, Std: {w_std:.4f}")
    print(f"Unwatermarked scores - Mean: {u_mean:.4f}, Std: {u_std:.4f}")
    print(f"Score separation     - {w_mean - u_mean:.4f}")
    
    # Recommend best threshold
    print("\n" + "=" * 40)
    print("Recommendations")
    print("=" * 40)
    
    print(f"📊 Score Distribution:")
    print(f"   • Watermarked: {w_mean:.3f} ± {w_std:.3f}")
    print(f"   • Unwatermarked: {u_mean:.3f} ± {u_std:.3f}")
    print(f"   • Separation: {w_mean - u_mean:.3f}")
    
    print(f"\n🎯 Optimization Results:")
    print(f"   • Best F1: F1={results_f1['F1']:.3f}, ACC={results_f1['ACC']:.3f}")
    print(f"   • FPR=5%: TPR={results_fpr['TPR']:.3f}, FPR={results_fpr['FPR']:.3f}")
    print(f"   • FPR=1%: TPR={results_conservative['TPR']:.3f}, FPR={results_conservative['FPR']:.3f}")
    
    # Current GGL threshold from config
    current_threshold = 0.05  # From config/GGL.json
    print(f"\n📋 Current GGL threshold: {current_threshold}")
    
    # Simple threshold recommendation
    suggested_threshold = (w_mean + u_mean) / 2
    print(f"💡 Suggested threshold (midpoint): {suggested_threshold:.4f}")
    
    return {
        'watermarked_scores': watermarked_scores,
        'unwatermarked_scores': unwatermarked_scores,
        'best_f1_results': results_f1,
        'target_fpr_results': results_fpr,
        'conservative_results': results_conservative,
        'suggested_threshold': suggested_threshold
    }

if __name__ == "__main__":
    try:
        results = find_ggl_best_threshold()
        print(f"\n🎉 Threshold optimization completed!")
    except Exception as e:
        print(f"\n❌ Error during threshold optimization: {e}")
        import traceback
        traceback.print_exc()
