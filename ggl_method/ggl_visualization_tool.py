#!/usr/bin/env python3
"""
GGL Watermark Visualization Tool
Creates visual representations of GGL watermarked vs unwatermarked text using MarkLLM visualizer.
"""

import sys
import torch
sys.path.insert(0, '/mnt/newhome/kasra/MarkLLM')

from watermark.auto_watermark import AutoWatermark
from utils.transformers_config import TransformersConfig
from transformers import AutoTokenizer
from visualize.visualizer import DiscreteVisualizer
from visualize.color_scheme import ColorSchemeForDiscreteVisualization
from visualize.font_settings import FontSettings
from visualize.page_layout_settings import PageLayoutSettings
from visualize.legend_settings import DiscreteLegendSettings

def create_ggl_visualizations():
    """Create and save GGL watermark visualizations."""
    
    print("GGL Watermark Visualization Tool")
    print("=" * 40)
    
    # Device
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")

    # Setup tokenizer and transformers config  
    tokenizer = AutoTokenizer.from_pretrained('GSAI-ML/LLaDA-8B-Instruct', trust_remote_code=True)
    transformers_config = TransformersConfig(
        model=None,  # GGL loads its own model
        tokenizer=tokenizer,
        vocab_size=tokenizer.vocab_size,
        device=device,
        max_new_tokens=200,
        min_length=50,
        do_sample=True,
        no_repeat_ngram_size=4
    )

    # Load GGL watermark using the standard pattern (adapted from KGW example)
    print("Loading GGL watermark...")
    myWatermark = AutoWatermark.load('GGL', 
                                     algorithm_config='config/GGL.json',
                                     transformers_config=transformers_config)
    print("✓ GGL watermark loaded")

    # Test prompt
    prompt = 'Explain the importance of artificial intelligence in modern society.'
    print(f"\nPrompt: {prompt}")

    # Generate texts
    print("\nGenerating watermarked text...")
    watermarked_text = myWatermark.generate_watermarked_text(prompt)
    print(f"Watermarked text: {watermarked_text[:100]}...")

    print("\nGenerating unwatermarked text...")
    unwatermarked_text = myWatermark.generate_unwatermarked_text(prompt)
    print(f"Unwatermarked text: {unwatermarked_text[:100]}...")

    # Detect watermarks
    watermarked_result = myWatermark.detect_watermark(watermarked_text, prompt=prompt)
    unwatermarked_result = myWatermark.detect_watermark(unwatermarked_text, prompt=prompt)
    
    print(f"\nWatermarked detection: {watermarked_result}")
    print(f"Unwatermarked detection: {unwatermarked_result}")

    # Get data for visualization (following the exact pattern you provided)
    print("\nPreparing visualization data...")
    watermarked_data = myWatermark.get_data_for_visualization(watermarked_text, prompt=prompt)
    unwatermarked_data = myWatermark.get_data_for_visualization(unwatermarked_text, prompt=prompt)

    print(f"Watermarked: {sum(watermarked_data.highlight_values)}/{len(watermarked_data.highlight_values)} green tokens")
    print(f"Unwatermarked: {sum(unwatermarked_data.highlight_values)}/{len(unwatermarked_data.highlight_values)} green tokens")

    # Init visualizer (following the exact pattern you provided)
    print("\nInitializing visualizer...")
    visualizer = DiscreteVisualizer(
        color_scheme=ColorSchemeForDiscreteVisualization(),
        font_settings=FontSettings(), 
        page_layout_settings=PageLayoutSettings(),
        legend_settings=DiscreteLegendSettings()
    )

    # Visualize (following the exact pattern you provided)
    print("Creating watermarked visualization...")
    watermarked_img = visualizer.visualize(
        data=watermarked_data, 
        show_text=True, 
        visualize_weight=True, 
        display_legend=True
    )

    print("Creating unwatermarked visualization...")
    unwatermarked_img = visualizer.visualize(
        data=unwatermarked_data,
        show_text=True, 
        visualize_weight=True, 
        display_legend=True
    )

    # Save (following the exact pattern you provided, but for GGL instead of KGW)
    print(f"\nSaving visualizations...")
    watermarked_img.save("GGL_watermarked.png")
    unwatermarked_img.save("GGL_unwatermarked.png")
    
    print(f"✓ Saved GGL_watermarked.png")
    print(f"✓ Saved GGL_unwatermarked.png")

    # Create summary report
    summary = f"""GGL Watermark Visualization Summary
==================================
Prompt: {prompt}

Watermarked Text:
{watermarked_text}

Unwatermarked Text:
{unwatermarked_text}

Detection Results:
- Watermarked: {watermarked_result}
- Unwatermarked: {unwatermarked_result}

Green Token Analysis:
- Watermarked: {sum(watermarked_data.highlight_values)}/{len(watermarked_data.highlight_values)} ({sum(watermarked_data.highlight_values)/len(watermarked_data.highlight_values)*100:.1f}%)
- Unwatermarked: {sum(unwatermarked_data.highlight_values)}/{len(unwatermarked_data.highlight_values)} ({sum(unwatermarked_data.highlight_values)/len(unwatermarked_data.highlight_values)*100:.1f}%)

Visualization Files:
- GGL_watermarked.png
- GGL_unwatermarked.png
- GGL_visualization_summary.txt
"""

    # Save summary
    with open("GGL_visualization_summary.txt", "w", encoding="utf-8") as f:
        f.write(summary)
    
    print("✓ Saved GGL_visualization_summary.txt")
    print("\n" + "=" * 40)
    print("🎉 GGL visualization complete!")
    print("Files created:")
    print("  • GGL_watermarked.png")
    print("  • GGL_unwatermarked.png") 
    print("  • GGL_visualization_summary.txt")

if __name__ == "__main__":
    create_ggl_visualizations()
