#!/usr/bin/env python3
"""
Test using GGL with the standard MarkLLM AutoWatermark pattern.
"""

import torch
import sys
sys.path.insert(0, '/mnt/newhome/kasra/MarkLLM')

from watermark.auto_watermark import AutoWatermark
from utils.transformers_config import TransformersConfig
from transformers import AutoTokenizer

def test_standard_pattern():
    """Test GGL using the standard MarkLLM pattern."""
    
    print("Testing GGL with Standard MarkLLM Pattern")
    print("=" * 45)
    
    try:
        # Device
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"Using device: {device}")

        # For GGL, we need the LLaDA tokenizer, not OPT
        # The model will be loaded automatically by GGL
        tokenizer = AutoTokenizer.from_pretrained('GSAI-ML/LLaDA-8B-Instruct', trust_remote_code=True)
        
        # Transformers config - note: model will be overridden by GGL
        transformers_config = TransformersConfig(
            model=None,  # GGL will load its own LLaDA model
            tokenizer=tokenizer,
            vocab_size=tokenizer.vocab_size,
            device=device,
            max_new_tokens=200,
            min_length=50,  # Adjusted for shorter tests
            do_sample=True,
            no_repeat_ngram_size=4
        )

        print("✓ Transformers config created")

        # Load watermark algorithm - change 'KGW' to 'GGL'
        print("Loading GGL watermark...")
        myWatermark = AutoWatermark.load('GGL', 
                                         algorithm_config='config/GGL.json',
                                         transformers_config=transformers_config)
        
        print("✓ GGL watermark loaded successfully")

        # Prompt
        prompt = 'Good Morning. Tell me about the weather today.'
        print(f"Prompt: {prompt}")

        # Generate watermarked text
        print("\nGenerating watermarked text...")
        watermarked_text = myWatermark.generate_watermarked_text(prompt)
        print(f"Watermarked: {watermarked_text}")

        # Detect watermark - NOTE: GGL needs the original prompt for detection
        print("\nDetecting watermark...")
        detect_result = myWatermark.detect_watermark(watermarked_text, prompt=prompt)
        print(f"Detection result: {detect_result}")

        # Generate unwatermarked text
        print("\nGenerating unwatermarked text...")
        unwatermarked_text = myWatermark.generate_unwatermarked_text(prompt)
        print(f"Unwatermarked: {unwatermarked_text}")

        # Detect on unwatermarked text
        print("\nDetecting on unwatermarked text...")
        detect_result_unwm = myWatermark.detect_watermark(unwatermarked_text, prompt=prompt)
        print(f"Detection result (unwatermarked): {detect_result_unwm}")

        print("\n" + "=" * 45)
        print("✅ Standard MarkLLM pattern works with GGL!")
        print("\nKey differences for GGL:")
        print("• Use LLaDA tokenizer instead of OPT")
        print("• Set model=None (GGL loads its own)")
        print("• Pass prompt= parameter to detect_watermark()")
        print("• Use algorithm name 'GGL' instead of 'KGW'")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_standard_pattern()
    sys.exit(0 if success else 1)
