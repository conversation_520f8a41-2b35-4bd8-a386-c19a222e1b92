# Copyright 2024 THU-BPM MarkLLM.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# ===============================================
# setup.py
# Description: Setup file for Cython compilation
# ===============================================

import numpy as np
from distutils.core import setup
from Cython.Build import cythonize

setup(
    name='levenshtein',
    ext_modules=cythonize('watermark/exp_edit/cython_files/levenshtein.pyx'),
    include_dirs = [np.get_include()]
)