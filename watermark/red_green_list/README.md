# Red-Green List Watermark Implementation

This directory contains the Red-Green List watermark implementation integrated with the MarkLLM framework.

## Overview

The Red-Green List watermark is a text watermarking method that applies a fixed bias (delta) to a predetermined set of "green" tokens during text generation. This is a simpler version compared to more sophisticated methods like GGL (Guided Green List).

## Key Features

- **Fixed Bias Approach**: Uses a constant delta value to bias green tokens
- **Deterministic Green List**: Green list is generated based on prompt and secret key
- **LLaDA Model Integration**: Works with the LLaDA-8B-Instruct model
- **MarkLLM Framework Compatible**: Fully integrated with MarkLLM evaluation pipelines

## Algorithm Details

1. **Green List Generation**: A fixed percentage of vocabulary tokens are selected as "green" based on a hash of the prompt and secret key
2. **Watermarked Generation**: During generation, green tokens receive a fixed positive bias (delta)
3. **Detection**: Watermark detection calculates the ratio of green tokens in the generated text

## Configuration

The watermark behavior is controlled by the configuration file `config/RedGreenList.json`:

- `secret_key`: Secret key for green list generation
- `green_list_ratio`: Percentage of vocabulary tokens in green list (default: 0.1)
- `delta`: Fixed bias value added to green tokens (default: 2.0)
- `cfg_scale`: Classifier-free guidance scale (default: 4.0)
- `gen_length`: Maximum generation length (default: 128)
- `steps`: Number of generation steps (default: 128)
- `detection_threshold`: Threshold for watermark detection (default: 0.2)

## Files

- `red_green_list.py`: Main implementation of the Red-Green List watermark
- `__init__.py`: Module initialization file

## Usage

### Basic Demo
```bash
cd /mnt/newhome/kasra/MarkLLM
python red_green_list_demo.py
```

### Full Evaluation
```bash
cd /mnt/newhome/kasra/MarkLLM
python red_green_list_evaluation.py
```

### F1 Score Evaluation
```bash
cd /mnt/newhome/kasra/MarkLLM
python red_green_list_f1_test.py
```

## Integration with MarkLLM

The Red-Green List watermark is fully integrated with the MarkLLM framework:

1. **Auto-loading**: Can be loaded using `AutoWatermark.load('RedGreenList', ...)`
2. **Standard Interface**: Implements the same interface as other MarkLLM watermarks
3. **Evaluation Pipelines**: Compatible with MarkLLM evaluation pipelines
4. **Visualization**: Provides data for visualization through `get_data_for_visualization()`

## Performance Notes

- The method provides a baseline for comparison with more sophisticated watermarking approaches
- Performance depends heavily on the delta value and green list ratio
- Works best with longer texts where statistical properties can be observed
- May have lower robustness compared to adaptive methods like GGL

## Dependencies

- PyTorch
- Transformers
- NumPy
- Scikit-learn (for evaluation metrics)
- MarkLLM framework components
