# Copyright 2024 THU-BPM MarkLLM.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# ============================================
# red_green_list.py
# Description: Implementation of Red-Green List watermark algorithm
# ============================================

import torch
import numpy as np
import torch.nn.functional as F
import hashlib
import random
from typing import Union
from ..base import BaseWatermark, BaseConfig
from utils.transformers_config import TransformersConfig
from transformers import AutoModel, AutoTokenizer
from visualize.data_for_visualization import DataForVisualization


class RedGreenListConfig(BaseConfig):
    """Config class for Red-Green List algorithm"""
    
    def initialize_parameters(self) -> None:
        """Initialize algorithm-specific parameters."""
        self.model_name = self.config_dict['model_name']
        self.secret_key = self.config_dict['secret_key']
        self.green_list_ratio = self.config_dict['green_list_ratio']
        self.cfg_scale = self.config_dict['cfg_scale']
        self.delta = self.config_dict['delta']
        self.gen_length = self.config_dict['gen_length']
        self.steps = self.config_dict['steps']
        self.detection_threshold = self.config_dict['detection_threshold']
        
        # Ensure device is a torch.device object
        if isinstance(self.device, str):
            self.device = torch.device(self.device)
        
        # Load the correct LLaDA model instead of using transformers_config.model
        print(f"Loading LLaDA model: {self.model_name}")
        self.generation_model = AutoModel.from_pretrained(
            self.model_name, 
            trust_remote_code=True,
            torch_dtype=torch.bfloat16 if self.device.type == 'cuda' else torch.float32
        ).to(self.device).eval()
        
        # Use tokenizer from transformers_config but update vocab_size for our model
        self.vocab_size = self.generation_model.config.vocab_size
    
    def algorithm_name(self) -> str:
        """Return the algorithm name."""
        return 'RedGreenList'


class RedGreenListUtils:
    """Utility class for Red-Green List algorithm, contains helper functions."""

    def __init__(self, config: RedGreenListConfig, *args, **kwargs) -> None:
        """Initialize the Red-Green List utility class."""
        self.config = config
        self.mask_id = 126336
        self.eos_id = 126081

    def get_green_list(self, prompt: str) -> torch.Tensor:
        """Generates a static green list of token IDs based on a prompt and secret key."""
        seed_material = f"{self.config.secret_key}|{prompt}"
        hashed_seed = hashlib.sha256(seed_material.encode()).hexdigest()
        rng = random.Random(hashed_seed)
        green_list_size = int(self.config.vocab_size * self.config.green_list_ratio)
        green_list = torch.tensor(rng.sample(range(self.config.vocab_size), green_list_size))
        return green_list.to(self.config.device)

    def get_num_transfer_tokens(self, mask_index: torch.Tensor, steps: int) -> torch.Tensor:
        """Calculates the number of tokens to unmask at each step for LLaDA."""
        mask_num = mask_index.sum(dim=1, keepdim=True)
        if steps == 0 or mask_num.item() == 0:
            return torch.zeros((mask_index.shape[0], steps), device=mask_index.device, dtype=torch.int64)
        base = mask_num // steps
        remainder = mask_num % steps
        num_transfer_tokens = torch.zeros(mask_num.size(0), steps, device=mask_index.device, dtype=torch.int64) + base
        for i in range(mask_num.size(0)):
            num_transfer_tokens[i, :remainder[i]] += 1
        return num_transfer_tokens

    def detect_watermark_ratio(self, prompt: str, text: str) -> float:
        """Detect watermark by calculating the ratio of green tokens."""
        if not text:
            return 0.0

        # Use the tokenizer from the config (following GGL pattern)
        token_ids = self.config.generation_tokenizer.encode(text, add_special_tokens=False)
        if not token_ids:
            return 0.0

        green_list = self.get_green_list(prompt)
        green_list_set = set(green_list.tolist())
        green_token_count = sum(1 for token_id in token_ids if token_id in green_list_set)
        return green_token_count / len(token_ids)


class RedGreenList(BaseWatermark):
    """Implementation of Red-Green List watermark algorithm."""
    
    def __init__(self, algorithm_config: str | RedGreenListConfig, transformers_config: TransformersConfig | None = None, *args, **kwargs) -> None:
        """
        Initialize the Red-Green List algorithm.

        Parameters:
            algorithm_config (str | RedGreenListConfig): Path to the algorithm configuration file or RedGreenListConfig instance.
            transformers_config (TransformersConfig): Configuration for the transformers model.
        """
        if isinstance(algorithm_config, str):
            self.config = RedGreenListConfig(algorithm_config, transformers_config)
        elif isinstance(algorithm_config, RedGreenListConfig):
            self.config = algorithm_config
        else:
            raise TypeError("algorithm_config must be either a path string or a RedGreenListConfig instance")
            
        self.utils = RedGreenListUtils(self.config)

    @torch.no_grad()
    def generate_watermarked_text(self, prompt: str, *args, **kwargs) -> str:
        """Generate watermarked text using Red-Green List method."""
        # Apply chat template (following GGL pattern)
        m = [{"role": "user", "content": prompt}]
        prompt_formatted = self.config.generation_tokenizer.apply_chat_template(
            m, add_generation_prompt=True, tokenize=False
        )
            
        # Tokenize the prompt
        input_ids = self.config.generation_tokenizer(
            prompt_formatted, return_tensors='pt'
        )['input_ids'].to(self.config.device)

        # Generate with watermark
        output = self._generate_with_red_green_list(
            input_ids, 
            gen_length=self.config.gen_length,
            steps=self.config.steps,
            cfg_scale=self.config.cfg_scale,
            delta=self.config.delta,
            secret_key=self.config.secret_key,
            prompt_for_seeding=prompt
        )
        
        # Decode the generated text (following GGL pattern)
        generated_text = self.config.generation_tokenizer.decode(output[0], skip_special_tokens=True)
        
        # Extract only the generated portion (remove the formatted prompt)
        prompt_formatted_text = self.config.generation_tokenizer.decode(input_ids[0], skip_special_tokens=True)
        if generated_text.startswith(prompt_formatted_text):
            generated_text = generated_text[len(prompt_formatted_text):].strip()
        
        return generated_text

    @torch.no_grad()
    def generate_unwatermarked_text(self, prompt: str, *args, **kwargs) -> str:
        """Generate unwatermarked text for comparison."""
        # Apply chat template (following GGL pattern)
        m = [{"role": "user", "content": prompt}]
        prompt_formatted = self.config.generation_tokenizer.apply_chat_template(
            m, add_generation_prompt=True, tokenize=False
        )
            
        # Tokenize the prompt
        input_ids = self.config.generation_tokenizer(
            prompt_formatted, return_tensors='pt'
        )['input_ids'].to(self.config.device)

        # Generate without watermark (secret_key=None)
        output = self._generate_with_red_green_list(
            input_ids, 
            gen_length=self.config.gen_length,
            steps=self.config.steps,
            cfg_scale=self.config.cfg_scale,
            delta=self.config.delta,
            secret_key=None,
            prompt_for_seeding=None
        )
        
        # Decode the generated text (following GGL pattern)
        generated_text = self.config.generation_tokenizer.decode(output[0], skip_special_tokens=True)
        
        # Extract only the generated portion (remove the formatted prompt)
        prompt_formatted_text = self.config.generation_tokenizer.decode(input_ids[0], skip_special_tokens=True)
        if generated_text.startswith(prompt_formatted_text):
            generated_text = generated_text[len(prompt_formatted_text):].strip()
        
        return generated_text

    def detect_watermark(self, text: str, prompt: str = None, return_dict: bool = True, *args, **kwargs) -> Union[tuple, dict]:
        """Detect watermark in the given text."""
        if prompt is None:
            # If no prompt provided, we can't detect the watermark
            score = 0.0
            is_watermarked = False
        else:
            # Calculate the ratio of green tokens
            score = self.utils.detect_watermark_ratio(prompt, text)
            is_watermarked = score > self.config.detection_threshold

        # Return results based on the return_dict flag (following GGL pattern)
        if return_dict:
            return {
                "is_watermarked": is_watermarked, 
                "score": score,
                "threshold": self.config.detection_threshold
            }
        else:
            return (is_watermarked, score)

    @torch.no_grad()
    def _generate_with_red_green_list(self, prompt_ids, gen_length=128, steps=128,
                                     cfg_scale=4.0, delta=2.0,
                                     secret_key: str = None, prompt_for_seeding: str = None):
        """
        Generates text using the Red-Green List watermark method.
        """
        model = self.config.generation_model

        x = torch.full((1, prompt_ids.shape[1] + gen_length), self.utils.mask_id, dtype=torch.long).to(model.device)
        x[:, :prompt_ids.shape[1]] = prompt_ids.clone()
        prompt_index = (x != self.utils.mask_id)

        green_list = None
        if secret_key and prompt_for_seeding:
            green_list = self.utils.get_green_list(prompt_for_seeding)

        for i in range(steps):
            mask_index = (x == self.utils.mask_id)
            if mask_index.sum() == 0:
                break

            # Start with standard CFG logits
            uncond_x = x.clone()
            uncond_x[prompt_index] = self.utils.mask_id
            input_x = torch.cat([x, uncond_x], dim=0)
            logits = model(input_x).logits
            cond_logits, uncond_logits = torch.chunk(logits, 2, dim=0)
            biased_logits = uncond_logits + cfg_scale * (cond_logits - uncond_logits)

            # Apply Red-Green List Watermark
            if green_list is not None:
                # Add a fixed, static delta to the green list tokens
                biased_logits[:, :, green_list] += delta

            # For testing, suppress EOS to get a full text
            biased_logits[:, :, self.utils.eos_id] = -float('inf')

            x0 = torch.argmax(biased_logits, dim=-1)
            
            # LLaDA's original sampling logic
            num_transfer_tokens = self.utils.get_num_transfer_tokens(mask_index, steps - i)
            p = F.softmax(biased_logits, dim=-1)
            x0_p = torch.squeeze(torch.gather(p, dim=-1, index=torch.unsqueeze(x0, -1)), -1)
            x0_p_masked = torch.where(mask_index, x0_p, -np.inf)
            transfer_index = torch.zeros_like(x0, dtype=torch.bool, device=x0.device)
            k_val = min(num_transfer_tokens[0, 0].item(), (x0_p_masked > -np.inf).sum().item())
            if k_val > 0:
                _, select_index = torch.topk(x0_p_masked.flatten(), k=k_val)
                transfer_index.view(-1)[select_index] = True
            x = torch.where(transfer_index, x0, x)

        return x

    def get_data_for_visualization(self, text: str, prompt: str = None, *args, **kwargs) -> DataForVisualization:
        """Get data for visualization purposes."""
        detection_result = self.detect_watermark(text, prompt)
        
        if prompt is None:
            # Without prompt, we can't determine green tokens (following GGL pattern)
            encoded_text = self.config.generation_tokenizer(text, return_tensors="pt", add_special_tokens=False)["input_ids"][0]
            decoded_tokens = [self.config.generation_tokenizer.decode(token_id.item()) for token_id in encoded_text]
            highlight_values = [0] * len(decoded_tokens)  # No highlighting without prompt
        else:
            # Encode text and get green list
            encoded_text = self.config.generation_tokenizer(text, return_tensors="pt", add_special_tokens=False)["input_ids"][0]
            green_list = self.utils.get_green_list(prompt)
            green_list_set = set(green_list.tolist())
            
            # Decode tokens and create highlight values
            decoded_tokens = []
            highlight_values = []
            for token_id in encoded_text:
                token = self.config.generation_tokenizer.decode(token_id.item())
                decoded_tokens.append(token)
                highlight_values.append(1 if token_id.item() in green_list_set else 0)
        
        return DataForVisualization(decoded_tokens, highlight_values)
