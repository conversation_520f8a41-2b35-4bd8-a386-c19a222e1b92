# GGL (Guided Green List) Watermark for MarkLLM

This directory contains the implementation of the Guided Green List (GGL) watermark algorithm integrated into the MarkLLM framework.

## Overview

The GGL watermark is specifically designed for LLaDA (Large Language Model for Dialogue Applications) models and uses a novel approach that combines:

- **Green List Generation**: Creates a deterministic list of "green" tokens based on the prompt and a secret key
- **Guided Sampling**: Applies additional guidance to green tokens during the generation process
- **CFG Integration**: Works seamlessly with Classifier-Free Guidance (CFG) in LLaDA models

## Files

- `ggl.py` - Main implementation of the GGL watermark algorithm
- `__init__.py` - Package initialization file
- `../config/GGL.json` - Configuration parameters for the GGL algorithm

## Configuration

The GGL algorithm can be configured via the `config/GGL.json` file:

```json
{
    "algorithm_name": "GGL",
    "model_name": "GSAI-ML/LLaDA-8B-Instruct",
    "secret_key": "default_ggl_secret_key_2024",
    "green_list_ratio": 0.1,
    "cfg_scale": 4.0,
    "guidance_delta": 2.0,
    "gen_length": 128,
    "steps": 128,
    "detection_threshold": 0.05
}
```

### Parameters

- `model_name`: The LLaDA model to use (automatically loaded with trust_remote_code=True)
- `secret_key`: Secret key for green list generation
- `green_list_ratio`: Fraction of vocabulary to include in green list (default: 0.1)
- `cfg_scale`: Base CFG guidance scale (default: 4.0)
- `guidance_delta`: Additional guidance for green tokens (default: 2.0)
- `gen_length`: Maximum generation length (default: 128)
- `steps`: Number of generation steps (default: 128)
- `detection_threshold`: Threshold for watermark detection (default: 0.05)

## Usage

### Basic Usage

```python
from watermark.ggl.ggl import GGL
from utils.transformers_config import TransformersConfig
from transformers import AutoTokenizer

# Setup
tokenizer = AutoTokenizer.from_pretrained("GSAI-ML/LLaDA-8B-Instruct", trust_remote_code=True)
transformers_config = TransformersConfig(
    model=None,  # GGL loads its own model
    tokenizer=tokenizer,
    vocab_size=tokenizer.vocab_size,
    device=device
)

# Initialize GGL
ggl = GGL("config/GGL.json", transformers_config)

# Generate watermarked text
prompt = "Write a story about AI:"
watermarked_text = ggl.generate_watermarked_text(prompt)

# Detect watermark
result = ggl.detect_watermark(watermarked_text, prompt=prompt)
print(result)  # {'is_watermarked': True, 'score': 0.125}
```

### Integration with MarkLLM

The GGL implementation follows the MarkLLM pattern and can be used with the framework's automatic watermark loading system:

```python
from watermark.auto_watermark import AutoWatermark

# Load GGL automatically
watermark = AutoWatermark.load("GGL", transformers_config)
```

## Key Features

1. **LLaDA Model Compatibility**: Specifically designed for LLaDA models with automatic model loading
2. **Prompt-Dependent Green Lists**: Uses deterministic seeding based on prompt + secret key
3. **CFG Integration**: Seamlessly integrates with Classifier-Free Guidance
4. **MarkLLM Framework Compliance**: Follows all MarkLLM patterns and interfaces
5. **Visualization Support**: Provides data for token-level watermark visualization

## Implementation Details

### Model Loading Override

The GGL implementation overrides the standard MarkLLM model loading to ensure compatibility with LLaDA:

- Ignores the model provided in `transformers_config`
- Loads the LLaDA model specified in the config with `trust_remote_code=True`
- Uses the tokenizer and device from `transformers_config`

### Green List Generation

Green lists are generated deterministically using:
1. Concatenation of secret key and prompt
2. SHA-256 hashing for reproducible randomness
3. Random sampling without replacement

### Watermark Detection

Detection works by:
1. Tokenizing the input text
2. Generating the green list for the given prompt
3. Calculating the ratio of green tokens in the text
4. Comparing against the detection threshold

## Testing

Run the test scripts to verify the implementation:

```bash
# Basic functionality test
python test_ggl_integration.py

# Full generation test
python test_ggl_generation.py

# Demonstration
python ggl_demo.py
```

## Requirements

- PyTorch
- Transformers library
- Access to GSAI-ML/LLaDA-8B-Instruct model
- CUDA-capable GPU (recommended for generation)

## Notes

- The implementation requires internet access to download the LLaDA model on first use
- Generation may be slow on CPU; GPU is recommended
- The secret key should be kept confidential in production use
- Detection requires the original prompt used for generation
