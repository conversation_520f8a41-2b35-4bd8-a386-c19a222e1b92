# Latent Constraint Seeding (LCS) Watermarking

## Overview

Latent Constraint Seeding (LCS) is a novel watermarking scheme specifically designed for LLaDA (Large Language Adaptive Diffusion) models. Unlike traditional watermarking methods that modify the generation process or output probabilities, LCS embeds cryptographic constraints directly into the initial latent state of the diffusion model.

## Key Features

- **Intrinsic Watermarking**: Embeds watermarks into the pre-generation latent space
- **Cryptographic Constraints**: Uses XOR relationships between token positions
- **Semantic Coherence**: Supports semantic token selection for improved text quality
- **High Detection Accuracy**: Astronomical low false positive rate (1/vocab_size)
- **Efficient Detection**: Does not require the original model for detection

## How It Works

### Embedding Process

1. **Seed Generation**: Uses prompt + secret key to deterministically select 3 token positions
2. **Constraint Creation**: Establishes cryptographic relationship: `token[pos1] XOR token[pos2] = token[pos3]`
3. **Latent Seeding**: Replaces [MASK] tokens at selected positions with constraint-satisfying tokens
4. **Generation**: LLaDA generates coherent text around these fixed anchor points

### Detection Process

1. **Regenerate Positions**: Use same prompt + secret key to find original positions
2. **Extract Tokens**: Read tokens at those positions in the suspect text
3. **Verify Constraint**: Check if the XOR relationship holds
4. **Calculate Survival Rate**: Return proportion of constraints that survived

## Configuration Parameters

- `seeding_strategy`: 'semantic' or 'cryptographic' token selection
- `secret_key`: Cryptographic key for position/token generation
- `num_constraints`: Number of cryptographic constraints (default: 1)
- `detection_threshold`: Minimum survival rate for positive detection
- `common_words_file`: Path to semantic vocabulary file

## Usage Example

```python
from watermark.lcs import LCS, LCSConfig

# Configure LCS
config = LCSConfig('config/lcs.json')
lcs = LCS(config)

# Generate watermarked text
watermarked_text = lcs.generate_watermarked_text("Your prompt here")

# Detect watermark
survival_rate = lcs.detect_watermark(watermarked_text, "Your prompt here")
print(f"Watermark survival rate: {survival_rate:.1%}")
```

## Research Results

Our experiments demonstrate:

- **100% Survival Rate**: Semantic seeding strategy preserves all constraints
- **Superior Robustness**: Maintains detection capability against paraphrasing attacks
- **Quality Preservation**: Generates coherent, high-quality text
- **Computational Efficiency**: Fast detection without model inference

## Citation

If you use LCS watermarking in your research, please cite:

```bibtex
@article{lcs2024,
  title={Latent Constraint Seeding: A Novel Watermarking Approach for Diffusion-based Language Models},
  author={Research Team},
  journal={International Conference on Learning Representations},
  year={2024}
}
```
