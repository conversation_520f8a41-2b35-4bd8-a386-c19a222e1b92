# Copyright 2024 THU-BPM MarkLLM.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# ============================================
# lcs.py
# Description: Implementation of Latent Constraint Seeding (LCS) algorithm
# ============================================

import torch
import numpy as np
import torch.nn.functional as F
import hashlib
import random
from typing import Union
from ..base import BaseWatermark, BaseConfig
from utils.transformers_config import TransformersConfig
from transformers import AutoModel, AutoTokenizer
from visualize.data_for_visualization import DataForVisualization
import os


class LCSConfig(BaseConfig):
    """Config class for LCS algorithm"""
    
    def initialize_parameters(self) -> None:
        """Initialize algorithm-specific parameters."""
        self.model_name = self.config_dict['model_name']
        self.secret_key = self.config_dict['secret_key']
        self.seeding_strategy = self.config_dict.get('seeding_strategy', 'semantic')
        self.num_constraints = self.config_dict.get('num_constraints', 1)
        self.cfg_scale = self.config_dict['cfg_scale']
        self.gen_length = self.config_dict['gen_length']
        self.steps = self.config_dict['steps']
        self.detection_threshold = self.config_dict.get('detection_threshold', 0.5)
        self.common_words_file = self.config_dict.get('common_words_file', 'common_words.txt')
        
        # Ensure device is a torch.device object
        if isinstance(self.device, str):
            self.device = torch.device(self.device)
        
        # Load the correct LLaDA model instead of using transformers_config.model
        print(f"Loading LLaDA model: {self.model_name}")
        self.generation_model = AutoModel.from_pretrained(
            self.model_name, 
            trust_remote_code=True,
            torch_dtype=torch.float16 if self.device.type == 'cuda' else torch.float32
        ).to(self.device)
        
        # Use tokenizer from transformers_config but update vocab_size for our model
        self.vocab_size = self.generation_model.config.vocab_size
    
    @property
    def algorithm_name(self) -> str:
        """Return the algorithm name."""
        return 'LCS'


class ConstraintGenerator:
    """Utility class for generating cryptographic constraints for LCS."""

    def __init__(self, tokenizer, common_words_file="common_words.txt"):
        self.tokenizer = tokenizer
        
        # Load common words and convert to token IDs
        self.semantic_token_ids = []
        try:
            # Try to find the file in multiple locations
            possible_paths = [
                common_words_file,
                f"watermark/lcs/{common_words_file}",
                f"/mnt/newhome/kasra/LLaDA/{common_words_file}",
                f"/mnt/newhome/kasra/MarkLLM/{common_words_file}"
            ]
            
            words_loaded = False
            for path in possible_paths:
                if os.path.exists(path):
                    with open(path, 'r', encoding='utf-8') as f:
                        common_words = [line.strip() for line in f if line.strip()]
                    words_loaded = True
                    print(f"Loaded common words from: {path}")
                    break
            
            if not words_loaded:
                raise FileNotFoundError("Common words file not found")
            
            for word in common_words:
                try:
                    # Tokenize each word and take the first token
                    token_ids = self.tokenizer.encode(word, add_special_tokens=False)
                    if token_ids:
                        self.semantic_token_ids.append(token_ids[0])
                except:
                    continue
            
            # Remove duplicates and ensure we have enough tokens
            self.semantic_token_ids = list(set(self.semantic_token_ids))
            print(f"Loaded {len(self.semantic_token_ids)} semantic token IDs")
            
        except FileNotFoundError:
            print(f"Warning: {common_words_file} not found. Using fallback semantic tokens.")
            # Fallback: use some common token IDs
            self.semantic_token_ids = list(range(1000, 2000))  # Safe range
    
    def generate(self, prompt, secret_key, gen_length, seeding_strategy='semantic'):
        """
        Generate seed positions and tokens for LCS watermarking.
        
        Args:
            prompt: Input prompt text
            secret_key: Secret key for reproducibility
            gen_length: Length of generation region
            seeding_strategy: 'cryptographic' or 'semantic'
        
        Returns:
            dict: Seed positions, tokens, and constraint info
        """
        # Create deterministic seed
        seed_material = f"{secret_key}|{prompt}|{seeding_strategy}"
        hashed_seed = hashlib.sha256(seed_material.encode()).hexdigest()
        seed = int(hashed_seed[:16], 16)
        rng = random.Random(seed)
        
        # Generate positions
        min_spacing = max(5, gen_length // 20)
        positions = []
        
        for _ in range(3):  # We need 3 positions for XOR constraint
            attempts = 0
            while attempts < 100:
                pos = rng.randint(5, gen_length - 5)
                if all(abs(pos - existing_pos) >= min_spacing for existing_pos in positions):
                    positions.append(pos)
                    break
                attempts += 1
        
        if len(positions) < 3:
            positions = [10, gen_length//3, 2*gen_length//3]
        
        pos1, pos2, pos3 = positions[:3]
        
        # Generate tokens based on strategy
        if seeding_strategy == 'semantic' and self.semantic_token_ids:
            # Use semantic tokens only
            token1 = rng.choice(self.semantic_token_ids)
            token2 = rng.choice(self.semantic_token_ids)
        else:  # cryptographic
            # Use full vocabulary range
            vocab_size = len(self.tokenizer.get_vocab())
            token1 = rng.randint(1000, min(50000, vocab_size - 1000))
            token2 = rng.randint(1000, min(50000, vocab_size - 1000))
        
        # Calculate third token using XOR constraint
        token3 = token1 ^ token2
        
        # For semantic strategy, ensure token3 is also semantic if possible
        if seeding_strategy == 'semantic' and self.semantic_token_ids and token3 not in self.semantic_token_ids:
            # Find a semantic token that satisfies the constraint
            for candidate in self.semantic_token_ids:
                if candidate == token1 ^ token2:
                    token3 = candidate
                    break
            else:
                # If no exact match, use modulo to map to semantic range
                token3 = self.semantic_token_ids[token3 % len(self.semantic_token_ids)]
        
        seed_tokens = {
            pos1: token1,
            pos2: token2,
            pos3: token3
        }
        
        constraint_info = {
            'type': 'xor',
            'positions': [pos1, pos2, pos3],
            'tokens': [token1, token2, token3],
            'relation': f'token[{pos1}] XOR token[{pos2}] = token[{pos3}]',
            'strategy': seeding_strategy
        }
        
        return {
            'seed_tokens': seed_tokens,
            'constraint_info': constraint_info,
            'positions': positions[:3]
        }


class LCSUtils:
    """Utility class for LCS algorithm, contains helper functions."""

    def __init__(self, config: LCSConfig, *args, **kwargs) -> None:
        """Initialize the LCS utility class."""
        self.config = config
        self.mask_id = 126336
        self.eos_id = 126081
        self.constraint_generator = ConstraintGenerator(
            self.config.generation_tokenizer, 
            self.config.common_words_file
        )

    def get_num_transfer_tokens(self, mask_index: torch.Tensor, steps: int) -> torch.Tensor:
        """Calculates the number of tokens to unmask at each step for LLaDA."""
        mask_num = mask_index.sum(dim=1, keepdim=True)
        if steps == 0 or mask_num.item() == 0:
            return torch.zeros((mask_index.shape[0], steps), device=mask_index.device, dtype=torch.int64)
        base = mask_num // steps
        remainder = mask_num % steps
        num_transfer_tokens = torch.zeros(mask_num.size(0), steps, device=mask_index.device, dtype=torch.int64) + base
        for i in range(mask_num.size(0)):
            num_transfer_tokens[i, :remainder[i]] += 1
        return num_transfer_tokens

    def detect_watermark_survival_rate(self, prompt: str, text: str) -> float:
        """
        Detect watermark by checking survival rate of seed constraints.
        
        Returns:
            float: Survival rate (0.0 to 1.0)
        """
        if not text:
            return 0.0
        
        # Regenerate the original seed information
        original_seed_info = self.constraint_generator.generate(
            prompt, self.config.secret_key, self.config.gen_length, self.config.seeding_strategy
        )
        
        # Tokenize the generated text
        try:
            tokens = self.config.generation_tokenizer.encode(text, add_special_tokens=False)
        except:
            return 0.0
        
        # Check each seeded position
        surviving_seeds = 0
        total_seeds = len(original_seed_info['seed_tokens'])
        
        for pos, expected_token in original_seed_info['seed_tokens'].items():
            if pos < len(tokens):
                actual_token = tokens[pos]
                if actual_token == expected_token:
                    surviving_seeds += 1
        
        survival_rate = surviving_seeds / total_seeds if total_seeds > 0 else 0.0
        return survival_rate


class LCS(BaseWatermark):
    """Top-level class for LCS algorithm."""

    def __init__(self, algorithm_config: str | LCSConfig, transformers_config: TransformersConfig | None = None, *args, **kwargs) -> None:
        """
        Initialize the LCS algorithm.

        Parameters:
            algorithm_config (str | LCSConfig): Path to the algorithm configuration file or LCSConfig instance.
            transformers_config (TransformersConfig): Configuration for the transformers model.
        """
        if isinstance(algorithm_config, str):
            self.config = LCSConfig(algorithm_config, transformers_config)
        elif isinstance(algorithm_config, LCSConfig):
            self.config = algorithm_config
        else:
            raise TypeError("algorithm_config must be either a path string or a LCSConfig instance")
            
        self.utils = LCSUtils(self.config)
    
    @torch.no_grad()
    def generate_watermarked_text(self, prompt: str, *args, **kwargs) -> str:
        """Generate watermarked text using Latent Constraint Seeding (LCS)."""
        
        # Format prompt using chat template (CRITICAL: matches original LLaDA implementation)
        m = [{"role": "user", "content": prompt}]
        prompt_formatted = self.config.generation_tokenizer.apply_chat_template(m, add_generation_prompt=True, tokenize=False)
        prompt_ids = self.config.generation_tokenizer(prompt_formatted, return_tensors='pt')['input_ids'].to(self.config.device)
        
        # Initialize generation tensor
        x = torch.full((1, prompt_ids.shape[1] + self.config.gen_length), self.utils.mask_id, dtype=torch.long).to(self.config.device)
        x[:, :prompt_ids.shape[1]] = prompt_ids.clone()
        prompt_index = (x != self.utils.mask_id)

        # Generate and apply seed constraints
        seed_info = self.utils.constraint_generator.generate(
            prompt, self.config.secret_key, self.config.gen_length, self.config.seeding_strategy
        )
        
        # Apply seeds to initial latent state
        prompt_length = prompt_ids.shape[1]
        for pos, token_id in seed_info['seed_tokens'].items():
            abs_pos = prompt_length + pos
            if abs_pos < x.shape[1]:
                x[0, abs_pos] = token_id

        # Generation loop
        for i in range(self.config.steps):
            mask_index = (x == self.utils.mask_id)
            if mask_index.sum() == 0:
                break

            # CFG setup
            uncond_x = x.clone()
            uncond_x[prompt_index] = self.utils.mask_id
            
            input_x = torch.cat([x, uncond_x], dim=0)
            logits = self.config.generation_model(input_x).logits
            cond_logits, uncond_logits = torch.chunk(logits, 2, dim=0)

            # Apply guidance
            biased_logits = uncond_logits + self.config.cfg_scale * (cond_logits - uncond_logits)
            biased_logits[:, :, self.utils.eos_id] = -float('inf')  # Suppress EOS

            # Standard token selection
            x0 = torch.argmax(biased_logits, dim=-1)

            # Calculate number of tokens to unmask
            mask_num = mask_index.sum(dim=1, keepdim=True)
            if mask_num.item() == 0:
                break
            
            base = mask_num // (self.config.steps - i)
            remainder = mask_num % (self.config.steps - i)
            num_transfer_tokens = base.item()
            if remainder.item() > 0:
                num_transfer_tokens += 1

            # Select tokens to unmask based on confidence
            p = F.softmax(biased_logits, dim=-1)
            x0_p = torch.squeeze(torch.gather(p, dim=-1, index=torch.unsqueeze(x0, -1)), -1)
            x0_p_masked = torch.where(mask_index, x0_p, -float('inf'))

            transfer_index = torch.zeros_like(x0, dtype=torch.bool, device=x0.device)
            k_val = min(num_transfer_tokens, (x0_p_masked > -float('inf')).sum().item())
            if k_val > 0:
                _, select_index = torch.topk(x0_p_masked.flatten(), k=k_val)
                transfer_index.view(-1)[select_index] = True

            x = torch.where(transfer_index, x0, x)

        # Extract generated text
        generated_text = self.config.generation_tokenizer.decode(
            x[0, prompt_ids.shape[1]:], skip_special_tokens=True
        )
        
        return generated_text

    def detect_watermark(self, text: str, prompt: str, return_dict: bool = True, *args, **kwargs) -> Union[bool, float, dict]:
        """
        Detect watermark in the given text.

        Parameters:
            text (str): The text to be detected.
            prompt (str): The original prompt used for generation.
            return_dict (bool): Whether to return results as a dictionary.

        Returns:
            Union[bool, float, dict]: Detection result - survival rate as float or dict with is_watermarked and score.
        """
        survival_rate = self.utils.detect_watermark_survival_rate(prompt, text)

        # Determine if watermarked based on survival rate and threshold
        is_watermarked = survival_rate > self.config.detection_threshold

        # Return results based on the return_dict flag
        if return_dict:
            return {"is_watermarked": is_watermarked, "score": survival_rate}
        else:
            return (is_watermarked, survival_rate)

    def get_data_for_visualization(self, text: str, prompt: str = None, *args, **kwargs) -> DataForVisualization:
        """
        Get data for visualization.

        Parameters:
            text (str): The watermarked text.
            prompt (str): The original prompt.

        Returns:
            DataForVisualization: Data for visualization.
        """
        if prompt is None:
            # Without prompt, we can't determine seeded tokens
            encoded_text = self.config.generation_tokenizer(text, return_tensors="pt", add_special_tokens=False)["input_ids"][0]
            decoded_tokens = [self.config.generation_tokenizer.decode(token_id.item()) for token_id in encoded_text]
            highlight_values = [0] * len(decoded_tokens)  # No highlighting without prompt
        else:
            # Get seed information for visualization
            seed_info = self.utils.constraint_generator.generate(
                prompt, self.config.secret_key, self.config.gen_length, self.config.seeding_strategy
            )
            
            # Tokenize the text
            encoded_text = self.config.generation_tokenizer(text, return_tensors="pt", add_special_tokens=False)["input_ids"][0]
            
            # Create highlight values for seeded positions
            decoded_tokens = []
            highlight_values = []
            seeded_positions = set(seed_info['positions'])
            
            for i, token_id in enumerate(encoded_text):
                token = self.config.generation_tokenizer.decode(token_id.item())
                decoded_tokens.append(token)
                # Highlight seeded positions
                highlight_values.append(1 if i in seeded_positions else 0)
        
        return DataForVisualization(decoded_tokens, highlight_values)
