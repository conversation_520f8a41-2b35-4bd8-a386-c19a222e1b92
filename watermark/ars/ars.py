# Copyright 2024 THU-BPM MarkLLM.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# ============================================
# ars.py
# Description: Implementation of Anchor-based Relative Seeding (ARS) algorithm
# ============================================

import torch
import numpy as np
import torch.nn.functional as F
import hashlib
import random
from typing import Union
from ..base import BaseWatermark, BaseConfig
from utils.transformers_config import TransformersConfig
from transformers import AutoModel, AutoTokenizer
from visualize.data_for_visualization import DataForVisualization
import sys
import os

# Add the ARS framework to the path
sys.path.append('/mnt/newhome/kasra/MarkLLM')
from ars_framework import (
    Anchor, ARSConstraint, ARSConstraintGenerator, 
    generate_with_ars, ARSWatermarkDetector, simple_insertion_attack
)


class ARSConfig(BaseConfig):
    """Config class for ARS algorithm"""
    
    def initialize_parameters(self) -> None:
        """Initialize algorithm-specific parameters."""
        self.model_name = self.config_dict['model_name']
        self.secret_key = self.config_dict['secret_key']
        self.num_anchors = self.config_dict.get('num_anchors', 3)
        self.cfg_scale = self.config_dict['cfg_scale']
        self.gen_length = self.config_dict['gen_length']
        self.steps = self.config_dict['steps']
        self.detection_threshold = self.config_dict.get('detection_threshold', True)  # Boolean for ARS
        self.common_words_file = self.config_dict.get('common_words_file', 'common_words.txt')
        
        # Ensure device is a torch.device object
        if isinstance(self.device, str):
            self.device = torch.device(self.device)
        
        # Load the correct LLaDA model
        print(f"Loading LLaDA model: {self.model_name}")
        self.generation_model = AutoModel.from_pretrained(
            self.model_name, 
            trust_remote_code=True,
            torch_dtype=torch.float16 if self.device.type == 'cuda' else torch.float32
        ).to(self.device)
        
        # Use tokenizer from transformers_config but update vocab_size for our model
        self.vocab_size = self.generation_model.config.vocab_size
    
    @property
    def algorithm_name(self) -> str:
        """Return the algorithm name."""
        return 'ARS'


class ARSUtils:
    """Utility class for ARS algorithm, contains helper functions."""

    def __init__(self, config: ARSConfig, *args, **kwargs) -> None:
        """Initialize the ARS utility class."""
        self.config = config
        self.mask_id = 126336
        self.eos_id = 126081
        self.constraint_generator = ARSConstraintGenerator(
            self.config.generation_tokenizer, 
            self.config.common_words_file
        )
        self.detector = ARSWatermarkDetector(
            self.config.generation_tokenizer,
            self.config.secret_key
        )

    def detect_watermark_presence(self, prompt: str, text: str) -> bool:
        """
        Detect watermark using ARS method.
        
        Returns:
            bool: True if watermark detected, False otherwise
        """
        return self.detector.detect(prompt, text)


class ARS(BaseWatermark):
    """Top-level class for ARS algorithm."""

    def __init__(self, algorithm_config: str | ARSConfig, transformers_config: TransformersConfig | None = None, *args, **kwargs) -> None:
        """
        Initialize the ARS algorithm.

        Parameters:
            algorithm_config (str | ARSConfig): Path to the algorithm configuration file or ARSConfig instance.
            transformers_config (TransformersConfig): Configuration for the transformers model.
        """
        if isinstance(algorithm_config, str):
            self.config = ARSConfig(algorithm_config, transformers_config)
        elif isinstance(algorithm_config, ARSConfig):
            self.config = algorithm_config
        else:
            raise TypeError("algorithm_config must be either a path string or a ARSConfig instance")
            
        self.utils = ARSUtils(self.config)
    
    @torch.no_grad()
    def generate_watermarked_text(self, prompt: str, *args, **kwargs) -> str:
        """Generate watermarked text using Anchor-based Relative Seeding (ARS)."""
        
        # Format prompt using chat template (CRITICAL: matches original LLaDA implementation)
        m = [{"role": "user", "content": prompt}]
        prompt_formatted = self.config.generation_tokenizer.apply_chat_template(m, add_generation_prompt=True, tokenize=False)
        prompt_ids = self.config.generation_tokenizer(prompt_formatted, return_tensors='pt')['input_ids'].to(self.config.device)
        
        # Use ARS two-pass generation
        output, ars_constraint = generate_with_ars(
            self.config.generation_model,
            self.config.generation_tokenizer,
            prompt_ids,
            gen_length=self.config.gen_length,
            steps=self.config.steps,
            cfg_scale=self.config.cfg_scale,
            secret_key=self.config.secret_key,
            prompt_for_seeding=prompt
        )
        
        # Store constraint for later use
        self.last_constraint = ars_constraint
        
        # Extract generated text
        generated_text = self.config.generation_tokenizer.decode(
            output[0, prompt_ids.shape[1]:], skip_special_tokens=True
        )
        
        return generated_text

    def detect_watermark(self, text: str, prompt: str, return_dict: bool = True, *args, **kwargs) -> Union[bool, float, dict]:
        """
        Detect watermark in the given text.

        Parameters:
            text (str): The text to be detected.
            prompt (str): The original prompt used for generation.
            return_dict (bool): Whether to return results as a dictionary.

        Returns:
            Union[bool, float, dict]: Detection result - True/False for ARS or dict with is_watermarked and score.
        """
        detection_result = self.utils.detect_watermark_presence(prompt, text)

        # For ARS, the detection result is already a boolean
        is_watermarked = bool(detection_result)
        score = float(detection_result)  # Convert boolean to float (1.0 or 0.0)

        # Return results based on the return_dict flag
        if return_dict:
            return {"is_watermarked": is_watermarked, "score": score}
        else:
            return (is_watermarked, score)

    def get_data_for_visualization(self, text: str, prompt: str, *args, **kwargs) -> DataForVisualization:
        """
        Get data for visualization.

        Parameters:
            text (str): The watermarked text.
            prompt (str): The original prompt.

        Returns:
            DataForVisualization: Data for visualization.
        """
        # Regenerate constraint for visualization
        ars_constraint = self.utils.constraint_generator.generate(prompt, self.config.secret_key, self.config.num_anchors)
        
        # Tokenize the text
        tokens = self.config.generation_tokenizer.encode(text, add_special_tokens=False)
        token_strs = [self.config.generation_tokenizer.decode([token]) for token in tokens]
        
        # Find positions to highlight (anchor words)
        highlight_values = [0.0] * len(token_strs)
        text_lower = text.lower()
        
        for anchor in ars_constraint.anchors:
            # Find anchor word in text and highlight nearby tokens
            anchor_start = text_lower.find(anchor.word.lower())
            if anchor_start != -1:
                # Convert character position to approximate token range
                text_before = text[:anchor_start]
                tokens_before = len(self.config.generation_tokenizer.encode(text_before, add_special_tokens=False))
                
                # Highlight the anchor and nearby tokens
                for i in range(max(0, tokens_before-1), min(len(highlight_values), tokens_before + anchor.relative_position + 2)):
                    highlight_values[i] = 1.0
        
        return DataForVisualization(
            self.config.algorithm_name,
            prompt,
            text,
            token_strs,
            highlight_values
        )
