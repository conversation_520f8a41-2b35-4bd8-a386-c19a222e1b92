#!/usr/bin/env python3
"""
Advanced Optimizer Demo

This script demonstrates the key features of the Advanced Optimizer for text watermarking methods.
"""

import sys
import os
import json
from pathlib import Path

def demo_method_discovery():
    """Demonstrate automatic method discovery."""
    print("=" * 60)
    print("DEMO: Automatic Watermarking Method Discovery")
    print("=" * 60)
    
    try:
        from advanced_optimizer import WatermarkMethodDiscoverer, setup_logging
        
        logger = setup_logging("INFO")
        discoverer = WatermarkMethodDiscoverer(logger)
        
        print("🔍 Scanning watermark directory for available methods...")
        
        if discoverer.initialize():
            methods = discoverer.discover_methods()
            
            print(f"\n✅ Discovery Complete!")
            print(f"📊 Found {len(methods)} watermarking methods:")
            
            for i, method in enumerate(methods, 1):
                print(f"\n{i}. Method: {method.name}")
                print(f"   📁 Config: {Path(method.config_path).name}")
                print(f"   🔧 Class: {method.algorithm_class}")
                
                # Show some parameters
                if method.parameters:
                    key_params = {k: v for k, v in method.parameters.items() 
                                if k not in ['algorithm_name', 'model_name', 'device']}
                    if key_params:
                        print(f"   ⚙️  Key Parameters: {list(key_params.keys())[:3]}...")
            
            return methods
        else:
            print("❌ Method discovery initialization failed")
            return []
            
    except Exception as e:
        print(f"❌ Method discovery failed: {e}")
        return []

def demo_hyperparameter_inference():
    """Demonstrate hyperparameter search space inference."""
    print("\n" + "=" * 60)
    print("DEMO: Intelligent Hyperparameter Search Space Inference")
    print("=" * 60)
    
    try:
        from advanced_optimizer import HyperparameterInferencer, setup_logging
        
        logger = setup_logging("INFO")
        inferencer = HyperparameterInferencer(logger)
        inferencer.initialize()
        
        # Sample configuration for demonstration
        sample_config = {
            "algorithm_name": "GGL",
            "model_name": "facebook/opt-1.3b",
            "secret_key": 42,
            "green_list_ratio": 0.5,
            "cfg_scale": 2.0,
            "guidance_delta": 1.5,
            "gen_length": 100,
            "steps": 10,
            "detection_threshold": 4.0,
            "device": "cpu"
        }
        
        print("🧠 Analyzing configuration to infer search spaces...")
        print(f"📋 Sample config: {json.dumps({k: v for k, v in sample_config.items() if k != 'device'}, indent=2)}")
        
        search_space = inferencer.infer_search_space(sample_config)
        
        print(f"\n✅ Search Space Inference Complete!")
        print(f"🎯 Inferred {len(search_space)} tunable parameters:")
        
        for param, space_def in search_space.items():
            if space_def[0] == 'float':
                print(f"   • {param}: Float({space_def[1]:.2f}, {space_def[2]:.2f})")
            elif space_def[0] == 'int':
                print(f"   • {param}: Int({space_def[1]}, {space_def[2]})")
            elif space_def[0] == 'categorical':
                print(f"   • {param}: Categorical({space_def[1]})")
        
        return search_space
        
    except Exception as e:
        print(f"❌ Hyperparameter inference failed: {e}")
        return {}

def demo_evaluation_job_discovery():
    """Demonstrate evaluation job discovery."""
    print("\n" + "=" * 60)
    print("DEMO: Dynamic Evaluation Job Discovery")
    print("=" * 60)
    
    try:
        from advanced_optimizer import EvaluationJobDiscoverer, setup_logging
        
        logger = setup_logging("INFO")
        job_discoverer = EvaluationJobDiscoverer(logger)
        
        print("🔍 Scanning evaluation directory for available jobs...")
        
        if job_discoverer.initialize():
            jobs = job_discoverer.discover_evaluation_jobs()
            
            print(f"\n✅ Job Discovery Complete!")
            print(f"📊 Found {len(jobs)} evaluation jobs:")
            
            # Group by type
            job_types = {}
            for job in jobs:
                if job.job_type not in job_types:
                    job_types[job.job_type] = []
                job_types[job.job_type].append(job)
            
            for job_type, type_jobs in job_types.items():
                print(f"\n📁 {job_type.upper()} Jobs ({len(type_jobs)}):")
                for job in type_jobs[:3]:  # Show first 3
                    print(f"   • {job.job_name}")
                if len(type_jobs) > 3:
                    print(f"   • ... and {len(type_jobs) - 3} more")
            
            return jobs
        else:
            print("❌ Job discovery initialization failed")
            return []
            
    except Exception as e:
        print(f"❌ Job discovery failed: {e}")
        return []

def demo_gpu_detection():
    """Demonstrate GPU detection for parallel execution."""
    print("\n" + "=" * 60)
    print("DEMO: GPU Detection for Parallel Execution")
    print("=" * 60)
    
    try:
        from advanced_optimizer import ParallelEvaluationExecutor, setup_logging
        
        logger = setup_logging("INFO")
        executor = ParallelEvaluationExecutor(logger)
        
        print("🔍 Detecting available GPU devices...")
        
        executor.initialize()
        gpus = executor.available_gpus
        
        print(f"\n✅ GPU Detection Complete!")
        if gpus:
            print(f"🎮 Found {len(gpus)} available GPUs: {gpus}")
            print("💡 Parallel execution will use GPU acceleration")
        else:
            print("💻 No GPUs detected - will use CPU execution")
            print("💡 Consider using GPU for faster evaluation")
        
        return gpus
        
    except Exception as e:
        print(f"❌ GPU detection failed: {e}")
        return []

def demo_cli_interface():
    """Demonstrate the CLI interface."""
    print("\n" + "=" * 60)
    print("DEMO: Command Line Interface")
    print("=" * 60)
    
    print("🖥️  Advanced Optimizer CLI Examples:")
    print()
    print("1️⃣  Optimize all methods with 50 trials:")
    print("   python3 advanced_optimizer.py --methods all --opt-trials 50")
    print()
    print("2️⃣  Optimize specific methods with custom quality penalty:")
    print("   python3 advanced_optimizer.py --methods ggl,lcs --quality-penalty 2.0")
    print()
    print("3️⃣  Skip optimization and run evaluation only:")
    print("   python3 advanced_optimizer.py --skip-optimization --methods all")
    print()
    print("4️⃣  Full workflow with custom evaluation path:")
    print("   python3 advanced_optimizer.py --methods all --opt-trials 100 --eval-path custom_eval/")
    print()
    print("5️⃣  Debug mode with detailed logging:")
    print("   python3 advanced_optimizer.py --methods ggl --log-level DEBUG --log-file debug.log")

def demo_configuration_management():
    """Demonstrate configuration management."""
    print("\n" + "=" * 60)
    print("DEMO: Configuration Management")
    print("=" * 60)
    
    try:
        from advanced_optimizer import ConfigurationManager, OptimizedConfiguration, setup_logging
        
        logger = setup_logging("INFO")
        config_manager = ConfigurationManager(logger)
        
        print("📁 Configuration Management Features:")
        print()
        
        # Demo optimized configuration format
        sample_config = OptimizedConfiguration(
            method_name="GGL",
            hyperparameters={
                "green_list_ratio": 0.35,
                "cfg_scale": 2.1,
                "guidance_delta": 1.8,
                "gen_length": 120,
                "steps": 12
            },
            detection_threshold=4.75,
            optimization_score=0.87,
            calibration_metrics={
                "roc_auc": 0.94,
                "validation_tpr": 0.89,
                "validation_fpr": 0.12
            },
            timestamp="2025-08-01 10:30:00"
        )
        
        print("✅ Optimized Configuration Example:")
        config_dict = {
            sample_config.method_name: {
                "hyperparameters": sample_config.hyperparameters,
                "detection_threshold": sample_config.detection_threshold,
                "optimization_score": sample_config.optimization_score,
                "calibration_metrics": sample_config.calibration_metrics
            }
        }
        
        print(json.dumps(config_dict, indent=2))
        
        print("\n💾 The system automatically saves optimized configurations to:")
        print("   • optimized_configurations.json (during execution)")
        print("   • results/final_optimized_configurations.json (final output)")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration demo failed: {e}")
        return False

def main():
    """Run the complete demonstration."""
    print("🚀 Advanced Optimizer for Text Watermarking Methods")
    print("   A sophisticated benchmark automation system")
    print()
    
    # Run all demonstrations
    methods = demo_method_discovery()
    search_space = demo_hyperparameter_inference()
    jobs = demo_evaluation_job_discovery()
    gpus = demo_gpu_detection()
    demo_cli_interface()
    demo_configuration_management()
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 DEMONSTRATION SUMMARY")
    print("=" * 60)
    print(f"📊 Discovered Methods: {len(methods)}")
    print(f"🎯 Tunable Parameters: {len(search_space)}")
    print(f"🔬 Evaluation Jobs: {len(jobs)}")
    print(f"🎮 Available GPUs: {len(gpus)}")
    print()
    print("✨ The Advanced Optimizer is ready for production use!")
    print("   Run with --help to see all available options.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
