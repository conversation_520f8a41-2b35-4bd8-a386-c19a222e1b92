# Advanced Optimizer for Text Watermarking Methods

A sophisticated master controller system for text watermarking benchmarks that automates discovery, optimization, and evaluation processes within the MarkLLM framework.

## 🎯 Overview

The Advanced Optimizer executes a sophisticated **three-phase process**:

1. **Hyperparameter Discovery & Optimization**: Uses Bayesian optimization to find optimal configurations
2. **Threshold Calibration**: Statistically determines optimal detection thresholds using ROC analysis  
3. **Comprehensive Evaluation**: Executes parallelized evaluations across multiple GPUs

## 🚀 Key Features

### 🔍 Dynamic Method Discovery
- Automatically scans `watermark/` directory to discover all available watermarking algorithms
- Intelligently loads and validates method configurations from `config/` directory
- No hardcoded method lists - future-proof and extensible

### 🧠 Intelligent Hyperparameter Inference
- Analyzes method configurations to automatically infer tunable parameter search spaces
- Uses type hints and default values to determine appropriate ranges
- Supports float, integer, and categorical parameter types

### ⚡ Bayesian Optimization
- Powered by Optuna for efficient hyperparameter search
- Custom objective function balancing detection performance and text quality
- Configurable quality penalty to control trade-offs

### 📊 Statistical Threshold Calibration  
- Uses ROC curve analysis to find optimal detection thresholds
- Implements <PERSON><PERSON>'s J statistic for robust threshold selection
- Validates thresholds on balanced datasets

### 🎮 GPU-Aware Parallel Execution
- Automatically detects available GPU devices
- Distributes evaluation jobs across GPUs in round-robin fashion
- Graceful fallback to CPU execution when GPUs unavailable

### 🔬 Comprehensive Evaluation Framework
- Dynamically discovers evaluation jobs from `evaluation/` directory
- Supports detection, quality analysis, and robustness testing
- Generates detailed reports in multiple formats

## 📁 Project Structure Integration

The optimizer integrates with the following project structure:

```
markllm/
├── watermark/                 # Watermarking methods (auto-discovered)
│   ├── ggl/
│   ├── lcs/
│   ├── red_green_list/
│   └── ...
├── config/                    # Method configurations (auto-loaded)
│   ├── GGL.json
│   ├── LCS.json
│   └── ...
├── evaluation/                # Evaluation framework (auto-scanned)
│   ├── pipelines/
│   └── tools/
└── advanced_optimizer.py      # Master controller
```

## 🛠️ Installation & Setup

### Prerequisites

```bash
# Required dependencies
pip install optuna scikit-learn numpy

# Optional for GPU support
pip install torch

# MarkLLM framework dependencies
pip install transformers datasets
```

### Quick Start

```bash
# Run demo to test functionality
python3 demo_advanced_optimizer.py

# Optimize all methods with 100 trials
python3 advanced_optimizer.py --methods all --opt-trials 100

# Optimize specific methods
python3 advanced_optimizer.py --methods ggl,lcs --opt-trials 50
```

## 📖 Usage Examples

### Basic Optimization

```bash
# Optimize all discovered methods
python3 advanced_optimizer.py --methods all --opt-trials 100

# Optimize specific methods with custom quality penalty
python3 advanced_optimizer.py --methods ggl,lcs --quality-penalty 2.0
```

### Advanced Configuration

```bash
# Full workflow with custom paths and logging
python3 advanced_optimizer.py \
    --methods all \
    --opt-trials 200 \
    --quality-penalty 1.5 \
    --eval-path custom_evaluation/ \
    --output-dir results_2025/ \
    --log-level DEBUG \
    --log-file optimization.log
```

### Skip Optimization (Evaluation Only)

```bash
# Use existing optimized configurations
python3 advanced_optimizer.py --skip-optimization --methods all
```

## 🎛️ Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--methods` | Methods to process (`all` or comma-separated list) | `all` |
| `--opt-trials` | Number of optimization trials per method | `100` |
| `--quality-penalty` | Lambda value for quality penalty in objective | `1.0` |
| `--skip-optimization` | Skip optimization and use existing configs | `False` |
| `--eval-path` | Path to evaluation directory | `evaluation` |
| `--max-workers` | Maximum parallel workers | `auto` |
| `--log-level` | Logging level (`DEBUG`, `INFO`, `WARNING`, `ERROR`) | `INFO` |
| `--log-file` | Optional log file path | `None` |
| `--output-dir` | Output directory for results | `results` |

## 📊 Output Files

The optimizer generates several output files:

### Configuration Files
- `optimized_configurations.json` - Intermediate optimized configs
- `results/final_optimized_configurations.json` - Final configs for reuse

### Reports
- `results/optimization_report.md` - Human-readable summary report
- `results/detailed_results.json` - Complete results in JSON format

### Example Output Structure

```json
{
  "GGL": {
    "hyperparameters": {
      "green_list_ratio": 0.35,
      "cfg_scale": 2.1,
      "guidance_delta": 1.8,
      "gen_length": 120,
      "steps": 12
    },
    "detection_threshold": 4.75,
    "optimization_score": 0.87,
    "roc_auc": 0.94
  }
}
```

## 🔬 Technical Architecture

### Phase 1: Hyperparameter Optimization

1. **Method Discovery**: Scan `watermark/` directory and validate configurations
2. **Search Space Inference**: Analyze parameters to determine optimization ranges
3. **Bayesian Optimization**: Use Optuna to maximize objective function:
   ```
   Objective = TPR - (λ × PPL_increase_ratio)
   ```

### Phase 2: Threshold Calibration

1. **Score Generation**: Generate detection scores on balanced validation set
2. **ROC Analysis**: Calculate ROC curve and find optimal threshold
3. **Youden's J**: Maximize `TPR - FPR` for robust threshold selection

### Phase 3: Comprehensive Evaluation

1. **Job Discovery**: Scan evaluation framework for available tests
2. **GPU Assignment**: Distribute jobs across available GPUs
3. **Parallel Execution**: Run evaluations using ProcessPoolExecutor
4. **Result Aggregation**: Collect and summarize all results

## 🏗️ Extending the System

### Adding New Watermarking Methods

1. Create method directory in `watermark/`
2. Add configuration file to `config/`
3. Optimizer will automatically discover and include it

### Adding New Evaluation Jobs

1. Add evaluation modules to `evaluation/pipelines/` or `evaluation/tools/`
2. Optimizer will automatically discover and include them

### Custom Objective Functions

Modify the `create_objective_function` method in `OptunaOptimizer` to implement custom optimization objectives.

## 🐛 Troubleshooting

### Common Issues

**No methods discovered**: Check that `watermark/` directory exists and contains valid method subdirectories.

**Optimization fails**: Ensure required dependencies (Optuna, scikit-learn) are installed.

**GPU not detected**: Verify CUDA installation and `nvidia-smi` availability.

**Import errors**: Check that all MarkLLM framework dependencies are installed.

### Debug Mode

Enable detailed logging for troubleshooting:

```bash
python3 advanced_optimizer.py --log-level DEBUG --log-file debug.log
```

## 📈 Performance Considerations

- **Optimization Speed**: Use fewer trials (`--opt-trials 50`) for faster testing
- **Memory Usage**: Reduce `--max-workers` if memory is limited  
- **GPU Memory**: Monitor GPU memory usage during parallel evaluation
- **Quality vs Speed**: Adjust `--quality-penalty` to balance optimization goals

## 🤝 Contributing

The Advanced Optimizer is designed to be extensible and maintainable:

- All components inherit from `BaseComponent` for consistent interfaces
- Error handling is centralized through `ErrorHandler` class
- Logging is comprehensive and configurable
- Code is well-documented with type hints

## 📄 License

Licensed under the Apache License, Version 2.0. See the LICENSE file for details.

## 🙏 Acknowledgments

Built on top of the MarkLLM framework for text watermarking research. Special thanks to the Optuna team for the excellent optimization library.
