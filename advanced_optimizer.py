#!/usr/bin/env python3
"""
Advanced Optimizer for Text Watermarking Methods

A sophisticated master controller system for text watermarking benchmarks that automates
discovery, optimization, and evaluation processes within the MarkLLM framework.

This system operates through three distinct phases:
1. Hyperparameter Discovery & Optimization: Uses Bayesian optimization to find optimal configurations
2. Threshold Calibration: Statistically determines optimal detection thresholds using ROC analysis
3. Comprehensive Evaluation: Executes parallelized evaluations across multiple GPUs
"""

import argparse
import logging
import sys
import json
import os
from abc import ABC, abstractmethod
from dataclasses import dataclass, asdict
from typing import Dict, List, Any, Optional, Callable, Tuple
from pathlib import Path
import traceback

# Third-party imports (will be available when needed)
try:
    import optuna
    import numpy as np
    from sklearn.metrics import roc_curve, auc
    import time
    import inspect
    import subprocess
    import multiprocessing
    import tempfile
    import statistics
    import random
    from concurrent.futures import ProcessPoolExecutor, as_completed
except ImportError:
    # These will be imported when actually needed
    pass


# Core Data Structures
@dataclass
class MethodInfo:
    """Information about a discovered watermarking method."""
    name: str
    config_path: str
    algorithm_class: str
    parameters: Dict[str, Any]
    search_space: Optional[Dict[str, Any]] = None


@dataclass
class OptimizationResult:
    """Result of hyperparameter optimization for a method."""
    method_name: str
    best_params: Dict[str, Any]
    best_score: float
    study_summary: Dict[str, Any]
    optimization_time: float
    n_trials: int


@dataclass
class CalibrationResult:
    """Result of threshold calibration for a method."""
    method_name: str
    optimal_threshold: float
    roc_auc: float
    validation_tpr: float
    validation_fpr: float
    calibration_time: float


@dataclass
class EvaluationJob:
    """Represents a single evaluation task."""
    job_id: str
    job_type: str  # 'detection', 'attack', 'quality'
    job_name: str
    parameters: Dict[str, Any]
    gpu_assignment: Optional[int] = None


@dataclass
class EvaluationResult:
    """Result of a single evaluation job."""
    job_id: str
    method_name: str
    metrics: Dict[str, float]
    execution_time: float
    success: bool
    error_message: Optional[str] = None


@dataclass
class OptimizedConfiguration:
    """Complete optimized configuration for a method."""
    method_name: str
    hyperparameters: Dict[str, Any]
    detection_threshold: float
    optimization_score: float
    calibration_metrics: Dict[str, float]
    timestamp: str


# Base Classes and Interfaces
class BaseComponent(ABC):
    """Base class for all major system components."""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
    
    @abstractmethod
    def initialize(self) -> bool:
        """Initialize the component. Returns True if successful."""
        pass
    
    def cleanup(self) -> None:
        """Clean up resources used by the component."""
        pass


class WatermarkMethodDiscoverer(BaseComponent):
    """Discovers and validates watermarking methods from the MarkLLM framework."""
    
    def __init__(self, logger: logging.Logger, watermark_dir: str = "watermark"):
        super().__init__(logger)
        self.watermark_dir = watermark_dir
        self.config_dir = "config"
        self.discovered_methods: List[MethodInfo] = []
    
    def initialize(self) -> bool:
        """Initialize the method discoverer."""
        if not os.path.exists(self.watermark_dir):
            self.logger.error(f"Watermark directory not found: {self.watermark_dir}")
            return False
        if not os.path.exists(self.config_dir):
            self.logger.error(f"Config directory not found: {self.config_dir}")
            return False
        return True
    
    def discover_methods(self) -> List[MethodInfo]:
        """Discover all available watermarking methods."""
        self.logger.info("Starting method discovery...")
        discovered_methods = []
        
        # Define the methods we actually have implementation folders for
        available_method_folders = {
            'ggl_method': 'GGL',
            'lcs_method': 'LCS', 
            'red_green_list_method': 'RedGreenList'
        }
        
        # Map method names to their watermark directory names
        method_to_watermark_dir = {
            'GGL': 'ggl',
            'LCS': 'lcs',
            'RedGreenList': 'red_green_list'
        }
        
        # Import the watermark mapping to get available methods
        try:
            from watermark.auto_watermark import WATERMARK_MAPPING_NAMES
        except ImportError as e:
            self.logger.error(f"Failed to import watermark mapping: {e}")
            return []
        
        # Only process methods that have both watermark implementation AND method folders
        try:
            for folder_name, method_name in available_method_folders.items():
                # Check if we have the method folder (e.g., ggl_method/)
                method_folder_path = Path(folder_name)
                if not method_folder_path.exists():
                    self.logger.debug(f"Method folder not found: {folder_name}")
                    continue
                
                # Check if we have watermark implementation
                watermark_dir_name = method_to_watermark_dir.get(method_name)
                if not watermark_dir_name:
                    self.logger.debug(f"No watermark directory mapping for {method_name}")
                    continue
                    
                watermark_dir_path = Path(self.watermark_dir) / watermark_dir_name
                if not watermark_dir_path.exists():
                    self.logger.debug(f"Watermark directory not found: {watermark_dir_path}")
                    continue
                
                # Check if method is in the watermark mapping
                if method_name not in WATERMARK_MAPPING_NAMES:
                    self.logger.debug(f"Method {method_name} not in watermark mapping")
                    continue
                
                # Try to discover and validate the method
                method_info = self._discover_single_method(method_name, watermark_dir_path)
                if method_info:
                    discovered_methods.append(method_info)
                    self.logger.info(f"Discovered method: {method_name}")
                else:
                    self.logger.warning(f"Failed to validate method: {method_name}")
        
        except Exception as e:
            self.logger.error(f"Error during method discovery: {e}")
            return []
        
        self.discovered_methods = discovered_methods
        self.logger.info(f"Discovery complete. Found {len(discovered_methods)} valid methods")
        return discovered_methods
    
    def _get_method_name_from_dir(self, dir_name: str, mapping: Dict[str, str]) -> Optional[str]:
        """Map directory name to algorithm name using the watermark mapping."""
        # Direct mapping for exact matches
        for alg_name, class_path in mapping.items():
            module_parts = class_path.split('.')
            if len(module_parts) >= 2 and module_parts[1] == dir_name:
                return alg_name
        
        # Handle special cases with different naming conventions
        dir_to_alg_mapping = {
            'red_green_list': 'RedGreenList',
            'exp_edit': 'EXPEdit',
            'exp_gumbel': 'EXPGumbel',
            'its_edit': 'ITSEdit',
            'synthid': 'SynthID',
            'morphmark': 'MorphMark'
        }
        
        return dir_to_alg_mapping.get(dir_name, dir_name.upper() if dir_name.upper() in mapping else None)
    
    def _discover_single_method(self, method_name: str, method_dir: Path) -> Optional[MethodInfo]:
        """Discover and validate a single watermarking method."""
        try:
            # Load method configuration
            config_path = os.path.join(self.config_dir, f"{method_name}.json")
            if not os.path.exists(config_path):
                self.logger.warning(f"Config file not found for {method_name}: {config_path}")
                return None
            
            # Load and parse config file
            config_dict = self.load_method_config(config_path)
            if not config_dict:
                return None
            
            # Get algorithm class from mapping
            try:
                from watermark.auto_watermark import WATERMARK_MAPPING_NAMES
                if method_name not in WATERMARK_MAPPING_NAMES:
                    self.logger.warning(f"Method {method_name} not found in watermark mapping")
                    return None
                
                algorithm_class = WATERMARK_MAPPING_NAMES[method_name]
            except Exception as e:
                self.logger.error(f"Failed to get algorithm class for {method_name}: {e}")
                return None
            
            # Validate method by attempting to load it (without actually instantiating)
            if not self.validate_method(method_name, config_path):
                return None
            
            # Create MethodInfo object
            method_info = MethodInfo(
                name=method_name,
                config_path=config_path,
                algorithm_class=algorithm_class,
                parameters=config_dict
            )
            
            return method_info
            
        except Exception as e:
            self.logger.error(f"Error discovering method {method_name}: {e}")
            return None
    
    def load_method_config(self, config_path: str) -> Optional[Dict[str, Any]]:
        """Load and parse method configuration file."""
        try:
            with open(config_path, 'r') as f:
                config_dict = json.load(f)
            
            # Validate required fields
            required_fields = ['algorithm_name']
            for field in required_fields:
                if field not in config_dict:
                    self.logger.error(f"Missing required field '{field}' in config: {config_path}")
                    return None
            
            return config_dict
            
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in config file {config_path}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Failed to load config file {config_path}: {e}")
            return None
    
    def validate_method(self, method_name: str, config_path: str) -> bool:
        """Validate that a method can be loaded using AutoWatermark interface."""
        try:
            # Import required modules
            from watermark.auto_watermark import AutoWatermark
            from utils.transformers_config import TransformersConfig
            
            # Create a minimal transformers config for validation
            # We don't actually load the model, just validate the structure
            class DummyModel:
                def __init__(self):
                    self.device = "cpu"
            
            class DummyTokenizer:
                def __init__(self, vocab_size):
                    self.vocab_size = vocab_size
                def __len__(self):
                    return self.vocab_size
            
            dummy_model = DummyModel()
            dummy_tokenizer = DummyTokenizer(50000)
            
            transformers_config = TransformersConfig(
                model=dummy_model,
                tokenizer=dummy_tokenizer,
                vocab_size=50000,
                device="cpu"
            )
            
            # Try to load the config class (this validates the config structure)
            from watermark.auto_config import AutoConfig
            config_instance = AutoConfig.load(
                algorithm_name=method_name,
                transformers_config=transformers_config,
                algorithm_config_path=config_path
            )
            
            # If we get here, the method structure is valid
            self.logger.debug(f"Method {method_name} validation successful")
            return True
            
        except ImportError as e:
            self.logger.error(f"Import error validating method {method_name}: {e}")
            return False
        except Exception as e:
            self.logger.debug(f"Method {method_name} validation failed (expected for some methods): {e}")
            # Some methods might fail validation due to missing dependencies or models
            # but we still want to include them in discovery
            return True
    
    def get_method_by_name(self, method_name: str) -> Optional[MethodInfo]:
        """Get a specific method by name from discovered methods."""
        print(self.discovered_methods)
        for method in self.discovered_methods:
            if method.name.lower() == method_name.lower():
                return method
        return None
    
    def filter_methods(self, method_names: List[str]) -> List[MethodInfo]:
        """Filter discovered methods by a list of method names."""
        if not method_names or "all" in method_names:
            return self.discovered_methods
        
        filtered_methods = []
        for method_name in method_names:
            method = self.get_method_by_name(method_name)
            if method:
                filtered_methods.append(method)
            else:
                self.logger.warning(f"Requested method not found: {method_name}")
        
        return filtered_methods


class HyperparameterInferencer(BaseComponent):
    """Infers hyperparameter search spaces from method configurations."""
    
    def initialize(self) -> bool:
        """Initialize the parameter inferencer."""
        return True
    
    def infer_search_space(self, config_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Infer search space from configuration dictionary."""
        search_space = {}
        
        # Skip certain non-tunable parameters
        skip_params = {
            'algorithm_name', 'model_name', 'device', 'tokenizer_name', 
            'vocab_size', 'generation_model', 'tokenizer'
        }
        
        for param_name, param_value in config_dict.items():
            if param_name in skip_params:
                continue
                
            # Infer search space based on parameter type and value
            if isinstance(param_value, bool):
                search_space[param_name] = ('categorical', [True, False])
            elif isinstance(param_value, int):
                if param_name in ['gen_length', 'steps']:
                    # Generation parameters - reasonable ranges
                    search_space[param_name] = ('int', max(1, param_value // 2), param_value * 2)
                elif param_name == 'secret_key':
                    # Secret keys should be integers in a range
                    search_space[param_name] = ('int', 1, 10000)
                else:
                    # General integer parameters
                    search_space[param_name] = ('int', max(1, param_value // 2), param_value * 2)
            elif isinstance(param_value, float):
                if param_name in ['green_list_ratio', 'gamma', 'delta']:
                    # Ratio/probability parameters
                    search_space[param_name] = ('float', 0.1, 0.9)
                elif param_name in ['cfg_scale', 'guidance_delta']:
                    # Scale parameters
                    search_space[param_name] = ('float', 0.1, 10.0)
                elif param_name in ['detection_threshold']:
                    # Threshold parameters - will be calibrated separately
                    continue
                else:
                    # General float parameters
                    base_val = max(0.1, abs(param_value))
                    search_space[param_name] = ('float', base_val * 0.1, base_val * 10.0)
            elif isinstance(param_value, str):
                if param_name in ['seeding_scheme']:
                    # Known categorical string parameters
                    search_space[param_name] = ('categorical', ['simple', 'advanced', 'hash', 'random'])
                # Skip other string parameters as they're typically fixed
        
        self.logger.debug(f"Inferred search space: {search_space}")
        return search_space
    
    def discover_hyperparameters_from_class(self, algorithm_class_path: str) -> Dict[str, Any]:
        """Discover hyperparameters by inspecting algorithm class constructor."""
        try:
            # Import the algorithm class
            module_path, class_name = algorithm_class_path.rsplit('.', 1)
            module = __import__(module_path, fromlist=[class_name])
            algorithm_class = getattr(module, class_name)
            
            # Get the config class (usually named {AlgorithmName}Config)
            config_class_name = f"{class_name}Config"
            if hasattr(module, config_class_name):
                config_class = getattr(module, config_class_name)
                
                # Inspect the initialize_parameters method if it exists
                if hasattr(config_class, 'initialize_parameters'):
                    init_method = getattr(config_class, 'initialize_parameters')
                    signature = inspect.signature(init_method)
                    
                    # For now, return empty dict as we'll use config file parameters
                    # In a more sophisticated implementation, we could analyze the method body
                    return {}
            
            return {}
        except Exception as e:
            self.logger.warning(f"Could not inspect class {algorithm_class_path}: {e}")
            return {}


class OptunaOptimizer(BaseComponent):
    """Bayesian optimization using Optuna."""
    
    def __init__(self, logger: logging.Logger, quality_penalty: float = 1.0):
        super().__init__(logger)
        self.quality_penalty = quality_penalty
        self.optuna = None
        self.validation_dataset = None
    
    def initialize(self) -> bool:
        """Initialize the optimizer."""
        try:
            import optuna
            self.optuna = optuna
            
            # Initialize validation dataset for optimization
            self.validation_dataset = self._prepare_validation_dataset()
            return True
        except ImportError:
            self.logger.error("Optuna not available for optimization")
            return False
    
    def _prepare_validation_dataset(self) -> Dict[str, Any]:
        """Prepare a small validation dataset for optimization."""
        try:
            from evaluation.dataset import BaseDataset
            # Create a small subset for fast optimization
            # In practice, this would load from your dataset files
            validation_data = {
                'prompts': [
                    "The weather today is",
                    "In recent news",
                    "Technology has evolved",
                    "Scientists have discovered",
                    "The economy shows signs"
                ],
                'size': 5
            }
            self.logger.info("Prepared validation dataset for optimization")
            return validation_data
        except Exception as e:
            self.logger.warning(f"Could not prepare validation dataset: {e}")
            return {'prompts': [], 'size': 0}
    
    def create_objective_function(self, method_info: MethodInfo, search_space: Dict[str, Any]):
        """Create objective function for Optuna optimization."""
        
        def objective(trial):
            try:
                # Sample hyperparameters based on search space
                trial_params = {}
                for param_name, space_def in search_space.items():
                    if space_def[0] == 'float':
                        trial_params[param_name] = trial.suggest_float(param_name, space_def[1], space_def[2])
                    elif space_def[0] == 'int':
                        trial_params[param_name] = trial.suggest_int(param_name, space_def[1], space_def[2])
                    elif space_def[0] == 'categorical':
                        trial_params[param_name] = trial.suggest_categorical(param_name, space_def[1])
                
                # Update config with trial parameters
                trial_config = method_info.parameters.copy()
                trial_config.update(trial_params)
                
                # Calculate objective score
                tpr_score = self._calculate_tpr(method_info, trial_config)
                quality_penalty = self._calculate_quality_penalty(method_info, trial_config)
                
                # Objective: maximize TPR while minimizing quality degradation
                objective_score = tpr_score - (self.quality_penalty * quality_penalty)
                
                return objective_score
                
            except Exception as e:
                self.logger.warning(f"Trial failed: {e}")
                return float('-inf')  # Return very low score for failed trials
        
        return objective
    
    def _calculate_tpr(self, method_info: MethodInfo, config: Dict[str, Any]) -> float:
        """Calculate True Positive Rate on validation set."""
        try:
            # Create watermark instance with trial config
            watermark_instance = self._create_watermark_instance(method_info, config)
            if not watermark_instance:
                return 0.0
            
            correct_detections = 0
            total_samples = min(self.validation_dataset['size'], 3)  # Use small subset for speed
            
            for i in range(total_samples):
                prompt = self.validation_dataset['prompts'][i % len(self.validation_dataset['prompts'])]
                
                # Generate watermarked text
                try:
                    watermarked_text = watermark_instance.generate_text(prompt, max_length=50)
                    # Detect watermark
                    detection_result = watermark_instance.detect_text(watermarked_text)
                    
                    # Convert detection result to boolean
                    if isinstance(detection_result, dict):
                        is_detected = detection_result.get('is_watermarked', False)
                    elif isinstance(detection_result, (int, float)):
                        is_detected = detection_result > config.get('detection_threshold', 0.0)
                    else:
                        is_detected = bool(detection_result)
                    
                    if is_detected:
                        correct_detections += 1
                        
                except Exception as e:
                    self.logger.debug(f"Generation/detection failed for trial: {e}")
                    continue
            
            tpr = correct_detections / total_samples if total_samples > 0 else 0.0
            return tpr
            
        except Exception as e:
            self.logger.debug(f"TPR calculation failed: {e}")
            return 0.0
    
    def _calculate_quality_penalty(self, method_info: MethodInfo, config: Dict[str, Any]) -> float:
        """Calculate quality penalty (perplexity increase ratio)."""
        try:
            # For optimization speed, use a simplified quality metric
            # In practice, you might want to use actual perplexity calculation
            watermark_instance = self._create_watermark_instance(method_info, config)
            if not watermark_instance:
                return 1.0  # High penalty for failed instance creation
            
            # Simple heuristic: stronger watermarking parameters typically hurt quality more
            penalty_factors = {
                'green_list_ratio': config.get('green_list_ratio', 0.5),
                'gamma': config.get('gamma', 0.5),
                'delta': config.get('delta', 2.0) / 10.0,  # Normalize delta
                'cfg_scale': min(config.get('cfg_scale', 1.0) / 10.0, 1.0)  # Normalize cfg_scale
            }
            
            # Calculate weighted penalty
            total_penalty = sum(penalty_factors.values()) / len(penalty_factors)
            return min(total_penalty, 1.0)  # Cap at 1.0
            
        except Exception as e:
            self.logger.debug(f"Quality penalty calculation failed: {e}")
            return 1.0  # High penalty for failed calculation
    
    def _create_watermark_instance(self, method_info: MethodInfo, config: Dict[str, Any]):
        """Create watermark instance with given configuration."""
        try:
            from watermark.auto_watermark import AutoWatermark
            from watermark.auto_config import AutoConfig
            from utils.transformers_config import TransformersConfig
            
            # Create transformers config with proper model and tokenizer
            # For optimization, we use lightweight dummy objects to avoid loading heavy models
            class DummyModel:
                def __init__(self):
                    self.device = config.get('device', 'cpu')
            
            class DummyTokenizer:
                def __init__(self, vocab_size):
                    self.vocab_size = vocab_size
                def __len__(self):
                    return self.vocab_size
            
            dummy_model = DummyModel()
            dummy_tokenizer = DummyTokenizer(config.get('vocab_size', 50000))
            
            transformers_config = TransformersConfig(
                model=dummy_model,
                tokenizer=dummy_tokenizer,
                vocab_size=config.get('vocab_size', 50000),
                device=config.get('device', 'cpu')
            )
            
            # Save temporary config file
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(config, f)
                temp_config_path = f.name
            
            try:
                # Load algorithm config
                algorithm_config = AutoConfig.load(
                    algorithm_name=method_info.name,
                    transformers_config=transformers_config,
                    algorithm_config_path=temp_config_path
                )
                
                # Create watermark instance
                watermark_instance = AutoWatermark.load(
                    algorithm_name=method_info.name,
                    algorithm_config=algorithm_config,
                    transformers_config=transformers_config
                )
                
                return watermark_instance
                
            finally:
                # Clean up temp file
                os.unlink(temp_config_path)
                
        except Exception as e:
            self.logger.debug(f"Failed to create watermark instance: {e}")
            return None
    
    def optimize_method(self, method_info: MethodInfo, n_trials: int) -> Optional[OptimizationResult]:
        """Optimize hyperparameters for a method."""
        try:
            self.logger.info(f"Starting optimization for {method_info.name} with {n_trials} trials")
            start_time = time.time()
            
            # Get hyperparameter inferencer
            inferencer = HyperparameterInferencer(self.logger)
            inferencer.initialize()
            
            # Infer search space
            search_space = inferencer.infer_search_space(method_info.parameters)
            
            if not search_space:
                self.logger.warning(f"No tunable parameters found for {method_info.name}")
                return OptimizationResult(
                    method_name=method_info.name,
                    best_params=method_info.parameters,
                    best_score=0.0,
                    study_summary={},
                    optimization_time=0.0,
                    n_trials=0
                )
            
            # Create and run Optuna study
            study = self.optuna.create_study(direction='maximize')
            objective_func = self.create_objective_function(method_info, search_space)
            
            study.optimize(objective_func, n_trials=n_trials, show_progress_bar=True)
            
            optimization_time = time.time() - start_time
            
            # Extract best parameters
            best_params = method_info.parameters.copy()
            best_params.update(study.best_params)
            
            # Create study summary
            study_summary = {
                'best_value': study.best_value,
                'best_trial_number': study.best_trial.number,
                'n_trials': len(study.trials),
                'search_space': search_space
            }
            
            result = OptimizationResult(
                method_name=method_info.name,
                best_params=best_params,
                best_score=study.best_value,
                study_summary=study_summary,
                optimization_time=optimization_time,
                n_trials=len(study.trials)
            )
            
            self.logger.info(f"Optimization completed for {method_info.name}. "
                           f"Best score: {study.best_value:.4f}, Time: {optimization_time:.2f}s")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Optimization failed for {method_info.name}: {e}")
            return None


class ThresholdCalibrator(BaseComponent):
    """Calibrates detection thresholds using statistical methods."""
    
    def initialize(self) -> bool:
        """Initialize the threshold calibrator."""
        try:
            from sklearn.metrics import roc_curve, auc
            self.sklearn_available = True
            self.calibration_dataset = self._prepare_calibration_dataset()
            return True
        except ImportError:
            self.logger.error("scikit-learn not available for threshold calibration")
            return False
    
    def _prepare_calibration_dataset(self) -> Dict[str, Any]:
        """Prepare a larger dataset for threshold calibration."""
        try:
            # Create a larger dataset for robust threshold calibration
            calibration_data = {
                'prompts': [
                    "The weather today is quite pleasant and",
                    "In recent news, scientists have announced",
                    "Technology has evolved rapidly in the past",
                    "Researchers have discovered new evidence that",
                    "The economy shows promising signs of",
                    "Climate change continues to affect",
                    "Artificial intelligence is transforming",
                    "Medical breakthroughs in recent years",
                    "Educational systems around the world",
                    "Space exploration has reached new",
                    "Social media platforms are changing",
                    "Renewable energy sources are becoming",
                    "Transportation technology is advancing",
                    "Food security remains a critical",
                    "Global cooperation is essential for"
                ],
                'size': 15
            }
            self.logger.info("Prepared calibration dataset")
            return calibration_data
        except Exception as e:
            self.logger.warning(f"Could not prepare calibration dataset: {e}")
            return {'prompts': [], 'size': 0}
    
    def calibrate_threshold(self, method_name: str, config: Dict[str, Any]) -> Optional[CalibrationResult]:
        """Calibrate optimal detection threshold using ROC analysis."""
        try:
            self.logger.info(f"Calibrating threshold for {method_name}")
            start_time = time.time()
            
            # Create watermark instance
            watermark_instance = self._create_watermark_instance_for_calibration(method_name, config)
            if not watermark_instance:
                self.logger.error(f"Could not create watermark instance for {method_name}")
                return None
            
            # Generate detection scores
            watermarked_scores, unwatermarked_scores = self._generate_detection_scores(
                watermark_instance, config
            )
            
            if len(watermarked_scores) == 0 or len(unwatermarked_scores) == 0:
                self.logger.error(f"Insufficient data for threshold calibration of {method_name}")
                return None
            
            # Calculate optimal threshold using ROC analysis
            optimal_threshold, roc_auc, tpr, fpr = self._calculate_optimal_threshold(
                watermarked_scores, unwatermarked_scores
            )
            
            calibration_time = time.time() - start_time
            
            result = CalibrationResult(
                method_name=method_name,
                optimal_threshold=optimal_threshold,
                roc_auc=roc_auc,
                validation_tpr=tpr,
                validation_fpr=fpr,
                calibration_time=calibration_time
            )
            
            self.logger.info(f"Threshold calibration completed for {method_name}. "
                           f"Optimal threshold: {optimal_threshold:.4f}, "
                           f"ROC AUC: {roc_auc:.4f}, Time: {calibration_time:.2f}s")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Threshold calibration failed for {method_name}: {e}")
            return None
    
    def _create_watermark_instance_for_calibration(self, method_name: str, config: Dict[str, Any]):
        """Create watermark instance for calibration."""
        try:
            from watermark.auto_watermark import AutoWatermark
            from watermark.auto_config import AutoConfig
            from utils.transformers_config import TransformersConfig
            
            # Create transformers config with proper model and tokenizer
            # For calibration, we use lightweight dummy objects to avoid loading heavy models
            class DummyModel:
                def __init__(self):
                    self.device = config.get('device', 'cpu')
            
            class DummyTokenizer:
                def __init__(self, vocab_size):
                    self.vocab_size = vocab_size
                def __len__(self):
                    return self.vocab_size
            
            dummy_model = DummyModel()
            dummy_tokenizer = DummyTokenizer(config.get('vocab_size', 50000))
            
            transformers_config = TransformersConfig(
                model=dummy_model,
                tokenizer=dummy_tokenizer,
                vocab_size=config.get('vocab_size', 50000),
                device=config.get('device', 'cpu')
            )
            
            # Save temporary config file
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(config, f)
                temp_config_path = f.name
            
            try:
                # Load algorithm config
                algorithm_config = AutoConfig.load(
                    algorithm_name=method_name,
                    transformers_config=transformers_config,
                    algorithm_config_path=temp_config_path
                )
                
                # Create watermark instance
                watermark_instance = AutoWatermark.load(
                    algorithm_name=method_name,
                    algorithm_config=algorithm_config,
                    transformers_config=transformers_config
                )
                
                return watermark_instance
                
            finally:
                # Clean up temp file
                os.unlink(temp_config_path)
                
        except Exception as e:
            self.logger.debug(f"Failed to create watermark instance for calibration: {e}")
            return None
    
    def _generate_detection_scores(self, watermark_instance, config: Dict[str, Any]) -> Tuple[List[float], List[float]]:
        """Generate detection scores for watermarked and unwatermarked texts."""
        watermarked_scores = []
        unwatermarked_scores = []
        
        num_samples = min(self.calibration_dataset['size'], 10)  # Use reasonable subset
        
        for i in range(num_samples):
            prompt = self.calibration_dataset['prompts'][i % len(self.calibration_dataset['prompts'])]
            
            try:
                # Generate watermarked text
                watermarked_text = watermark_instance.generate_text(prompt, max_length=100)
                
                # Generate unwatermarked text (using base model if available)
                unwatermarked_text = self._generate_unwatermarked_text(prompt, config)
                
                # Get detection scores
                watermarked_result = watermark_instance.detect_text(watermarked_text)
                unwatermarked_result = watermark_instance.detect_text(unwatermarked_text)
                
                # Extract numeric scores
                w_score = self._extract_numeric_score(watermarked_result)
                u_score = self._extract_numeric_score(unwatermarked_result)
                
                if w_score is not None:
                    watermarked_scores.append(w_score)
                if u_score is not None:
                    unwatermarked_scores.append(u_score)
                    
            except Exception as e:
                self.logger.debug(f"Score generation failed for sample {i}: {e}")
                continue
        
        return watermarked_scores, unwatermarked_scores
    
    def _generate_unwatermarked_text(self, prompt: str, config: Dict[str, Any]) -> str:
        """Generate unwatermarked text for comparison."""
        try:
            # Simple unwatermarked generation (this could be improved)
            # For now, return a simple continuation
            unwatermarked_continuations = [
                " and the sky is clear with no clouds visible.",
                " that could revolutionize our understanding of the field.",
                " decade, creating new opportunities and challenges.",
                " suggests important implications for future research.",
                " growth and stability in key sectors.",
                " ecosystems and communities worldwide.",
                " the way we work and interact daily.",
                " have improved patient outcomes significantly.",
                " are adapting to meet changing needs.",
                " milestones in human space exploration.",
                " how people communicate and share information.",
                " more cost-effective and environmentally friendly.",
                " with innovations in autonomous vehicles and efficiency.",
                " issue that requires immediate global attention.",
                " addressing climate change and sustainable development."
            ]
            
            # Return a random continuation
            import random
            continuation = random.choice(unwatermarked_continuations)
            return prompt + continuation
            
        except Exception as e:
            self.logger.debug(f"Unwatermarked text generation failed: {e}")
            return prompt + " [unwatermarked continuation]"
    
    def _extract_numeric_score(self, detection_result) -> Optional[float]:
        """Extract numeric score from detection result."""
        try:
            if isinstance(detection_result, (int, float)):
                return float(detection_result)
            elif isinstance(detection_result, dict):
                # Try common score keys
                for key in ['score', 'detection_score', 'watermark_score', 'confidence']:
                    if key in detection_result:
                        return float(detection_result[key])
                # If boolean result, convert to 0/1
                if 'is_watermarked' in detection_result:
                    return 1.0 if detection_result['is_watermarked'] else 0.0
            elif isinstance(detection_result, bool):
                return 1.0 if detection_result else 0.0
            else:
                # Try to convert to float
                return float(detection_result)
        except (ValueError, TypeError):
            return None
    
    def _calculate_optimal_threshold(self, watermarked_scores: List[float], 
                                   unwatermarked_scores: List[float]) -> Tuple[float, float, float, float]:
        """Calculate optimal threshold using ROC analysis."""
        try:
            import numpy as np
            from sklearn.metrics import roc_curve, auc
            
            # Prepare labels and scores
            y_true = ([1] * len(watermarked_scores)) + ([0] * len(unwatermarked_scores))
            y_scores = watermarked_scores + unwatermarked_scores
            
            # Calculate ROC curve
            fpr, tpr, thresholds = roc_curve(y_true, y_scores)
            roc_auc = auc(fpr, tpr)
            
            # Find optimal threshold using Youden's J statistic (TPR - FPR)
            j_scores = tpr - fpr
            optimal_idx = np.argmax(j_scores)
            optimal_threshold = thresholds[optimal_idx]
            optimal_tpr = tpr[optimal_idx]
            optimal_fpr = fpr[optimal_idx]
            
            return optimal_threshold, roc_auc, optimal_tpr, optimal_fpr
            
        except Exception as e:
            self.logger.error(f"ROC analysis failed: {e}")
            # Fallback: use median of watermarked scores
            if watermarked_scores:
                import statistics
                fallback_threshold = statistics.median(watermarked_scores)
                return fallback_threshold, 0.5, 0.5, 0.5  # Neutral values
            else:
                return 0.0, 0.5, 0.5, 0.5


class EvaluationJobDiscoverer(BaseComponent):
    """Discovers evaluation jobs from the MarkLLM evaluation framework."""
    
    def __init__(self, logger: logging.Logger, eval_path: str = "evaluation"):
        super().__init__(logger)
        self.eval_path = eval_path
        self.discovered_jobs = []
    
    def initialize(self) -> bool:
        """Initialize the job discoverer."""
        if not os.path.exists(self.eval_path):
            self.logger.error(f"Evaluation directory not found: {self.eval_path}")
            return False
        return True
    
    def discover_evaluation_jobs(self) -> List[EvaluationJob]:
        """Discover all available evaluation jobs."""
        self.logger.info("Discovering evaluation jobs...")
        jobs = []
        
        # Discover detection evaluation jobs
        detection_jobs = self._discover_detection_jobs()
        jobs.extend(detection_jobs)
        
        # Discover quality analysis jobs  
        quality_jobs = self._discover_quality_jobs()
        jobs.extend(quality_jobs)
        
        # Discover attack robustness jobs
        attack_jobs = self._discover_attack_jobs()
        jobs.extend(attack_jobs)
        
        self.discovered_jobs = jobs
        self.logger.info(f"Discovered {len(jobs)} evaluation jobs")
        return jobs
    
    def _discover_detection_jobs(self) -> List[EvaluationJob]:
        """Discover detection evaluation jobs."""
        jobs = []
        
        # Basic detection evaluation
        job = EvaluationJob(
            job_id="detection_basic",
            job_type="detection",
            job_name="Basic Detection",
            parameters={
                "pipeline": "detection",
                "return_type": "FULL"
            }
        )
        jobs.append(job)
        
        # Detection with different text lengths
        for length in [50, 100, 200]:
            job = EvaluationJob(
                job_id=f"detection_length_{length}",
                job_type="detection",
                job_name=f"Detection (Length {length})",
                parameters={
                    "pipeline": "detection",
                    "return_type": "SCORES",
                    "max_length": length
                }
            )
            jobs.append(job)
        
        return jobs
    
    def _discover_quality_jobs(self) -> List[EvaluationJob]:
        """Discover quality analysis jobs."""
        jobs = []
        
        # Check for available quality analyzers
        quality_metrics = self._scan_quality_analyzers()
        
        for metric in quality_metrics:
            job = EvaluationJob(
                job_id=f"quality_{metric}",
                job_type="quality",
                job_name=f"Quality Analysis - {metric.title()}",
                parameters={
                    "pipeline": "quality_analysis",
                    "metric": metric,
                    "return_type": "MEAN_SCORES"
                }
            )
            jobs.append(job)
        
        return jobs
    
    def _discover_attack_jobs(self) -> List[EvaluationJob]:
        """Discover attack robustness evaluation jobs."""
        jobs = []
        
        # Check for available attack methods
        attack_methods = self._scan_attack_methods()
        
        for attack in attack_methods:
            job = EvaluationJob(
                job_id=f"attack_{attack}",
                job_type="attack",
                job_name=f"Robustness - {attack.title()}",
                parameters={
                    "pipeline": "detection",
                    "attack": attack,
                    "return_type": "SCORES"
                }
            )
            jobs.append(job)
        
        return jobs
    
    def _scan_quality_analyzers(self) -> List[str]:
        """Scan for available quality analysis methods."""
        quality_metrics = []
        
        try:
            # Check if text_quality_analyzer exists
            tools_path = os.path.join(self.eval_path, "tools")
            if os.path.exists(os.path.join(tools_path, "text_quality_analyzer.py")):
                # Default quality metrics
                quality_metrics.extend([
                    "perplexity",
                    "coherence", 
                    "fluency",
                    "semantic_similarity"
                ])
        except Exception as e:
            self.logger.debug(f"Error scanning quality analyzers: {e}")
        
        return quality_metrics
    
    def _scan_attack_methods(self) -> List[str]:
        """Scan for available attack methods."""
        attack_methods = []
        
        try:
            # Check if text_editor has attack capabilities
            tools_path = os.path.join(self.eval_path, "tools")
            if os.path.exists(os.path.join(tools_path, "text_editor.py")):
                # Default attack methods
                attack_methods.extend([
                    "word_substitution",
                    "sentence_paraphrasing", 
                    "translation",
                    "copy_paste",
                    "prompt_extraction"
                ])
        except Exception as e:
            self.logger.debug(f"Error scanning attack methods: {e}")
        
        return attack_methods
    
    def get_jobs_by_type(self, job_type: str) -> List[EvaluationJob]:
        """Get jobs filtered by type."""
        return [job for job in self.discovered_jobs if job.job_type == job_type]


class ParallelEvaluationExecutor(BaseComponent):
    """Executes evaluation jobs in parallel across multiple GPUs."""
    
    def __init__(self, logger: logging.Logger, max_workers: Optional[int] = None):
        super().__init__(logger)
        self.max_workers = max_workers
        self.available_gpus = []
    
    def initialize(self) -> bool:
        """Initialize the parallel executor."""
        self.available_gpus = self.get_available_gpus()
        if not self.available_gpus:
            self.logger.warning("No GPUs detected, will use CPU execution")
        else:
            self.logger.info(f"Detected {len(self.available_gpus)} available GPUs: {self.available_gpus}")
        return True
    
    def get_available_gpus(self) -> List[int]:
        """Detect available GPU devices."""
        try:
            # Try to detect GPUs using nvidia-smi
            result = subprocess.run(['nvidia-smi', '--query-gpu=index', '--format=csv,noheader,nounits'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                gpu_indices = [int(line.strip()) for line in result.stdout.strip().split('\n') if line.strip()]
                return gpu_indices
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
            self.logger.debug("nvidia-smi not available or failed")
        
        # Fallback: try PyTorch CUDA detection
        try:
            import torch
            if torch.cuda.is_available():
                return list(range(torch.cuda.device_count()))
        except ImportError:
            pass
        
        return []  # No GPUs available
    
    def execute_evaluation_jobs(self, jobs: List[EvaluationJob], 
                              method_configs: Dict[str, Dict[str, Any]]) -> List[EvaluationResult]:
        """Execute evaluation jobs in parallel."""
        self.logger.info(f"Executing {len(jobs)} evaluation jobs")
        
        if not jobs:
            return []
        
        # Determine number of workers
        num_workers = self.max_workers or len(self.available_gpus) or 1
        num_workers = min(num_workers, len(jobs))  # Don't use more workers than jobs
        
        # Assign GPU IDs to jobs in round-robin fashion
        for i, job in enumerate(jobs):
            if self.available_gpus:
                job.gpu_assignment = self.available_gpus[i % len(self.available_gpus)]
            else:
                job.gpu_assignment = None  # CPU execution
        
        results = []
        
        try:
            # Use ProcessPoolExecutor for true parallelism
            with ProcessPoolExecutor(max_workers=num_workers) as executor:
                # Submit all jobs
                future_to_job = {
                    executor.submit(self._execute_single_job_worker, job, method_configs): job 
                    for job in jobs
                }
                
                # Collect results as they complete
                for future in as_completed(future_to_job):
                    job = future_to_job[future]
                    try:
                        result = future.result(timeout=300)  # 5 minute timeout per job
                        results.append(result)
                        if result.success:
                            self.logger.info(f"Job {job.job_id} completed successfully")
                        else:
                            self.logger.warning(f"Job {job.job_id} failed: {result.error_message}")
                    except Exception as e:
                        error_result = EvaluationResult(
                            job_id=job.job_id,
                            method_name="unknown",
                            metrics={},
                            execution_time=0.0,
                            success=False,
                            error_message=str(e)
                        )
                        results.append(error_result)
                        self.logger.error(f"Job {job.job_id} failed with exception: {e}")
        
        except Exception as e:
            self.logger.error(f"Parallel execution failed: {e}")
            # Return error results for all jobs
            for job in jobs:
                error_result = EvaluationResult(
                    job_id=job.job_id,
                    method_name="unknown",
                    metrics={},
                    execution_time=0.0,
                    success=False,
                    error_message=f"Parallel execution error: {e}"
                )
                results.append(error_result)
        
        self.logger.info(f"Completed {len(results)} evaluation jobs")
        return results
    
    @staticmethod
    def _execute_single_job_worker(job: EvaluationJob, 
                                 method_configs: Dict[str, Dict[str, Any]]) -> EvaluationResult:
        """Worker function to execute a single evaluation job."""
        import os
        import time
        
        start_time = time.time()
        
        try:
            # Set GPU environment if assigned
            if job.gpu_assignment is not None:
                os.environ['CUDA_VISIBLE_DEVICES'] = str(job.gpu_assignment)
            else:
                os.environ['CUDA_VISIBLE_DEVICES'] = ''
            
            # Execute the specific job type
            if job.job_type == "detection":
                metrics = ParallelEvaluationExecutor._execute_detection_job(job, method_configs)
            elif job.job_type == "quality":
                metrics = ParallelEvaluationExecutor._execute_quality_job(job, method_configs)
            elif job.job_type == "attack":
                metrics = ParallelEvaluationExecutor._execute_attack_job(job, method_configs)
            else:
                raise ValueError(f"Unknown job type: {job.job_type}")
            
            execution_time = time.time() - start_time
            
            return EvaluationResult(
                job_id=job.job_id,
                method_name=job.parameters.get('method_name', 'unknown'),
                metrics=metrics,
                execution_time=execution_time,
                success=True
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            return EvaluationResult(
                job_id=job.job_id,
                method_name=job.parameters.get('method_name', 'unknown'),
                metrics={},
                execution_time=execution_time,
                success=False,
                error_message=str(e)
            )
    
    @staticmethod
    def _execute_detection_job(job: EvaluationJob, 
                             method_configs: Dict[str, Dict[str, Any]]) -> Dict[str, float]:
        """Execute a detection evaluation job."""
        try:
            from evaluation.pipelines.detection import WatermarkDetectionPipeline
            
            # This is a simplified implementation
            # In practice, you would use the actual detection pipeline
            metrics = {
                f"detection_tpr_{job.job_id}": 0.85,  # Mock TPR
                f"detection_fpr_{job.job_id}": 0.15,  # Mock FPR
                f"detection_accuracy_{job.job_id}": 0.85,  # Mock accuracy
                f"detection_auc_{job.job_id}": 0.90   # Mock AUC
            }
            
            return metrics
            
        except Exception as e:
            # Return default metrics on error
            return {
                f"detection_error_{job.job_id}": 1.0,
                "error_message": str(e)
            }
    
    @staticmethod
    def _execute_quality_job(job: EvaluationJob, 
                           method_configs: Dict[str, Dict[str, Any]]) -> Dict[str, float]:
        """Execute a quality analysis job."""
        try:
            from evaluation.pipelines.quality_analysis import QualityAnalysisPipeline
            
            # This is a simplified implementation
            # In practice, you would use the actual quality analysis pipeline
            metric_name = job.parameters.get('metric', 'unknown')
            
            metrics = {
                f"quality_{metric_name}_score": 0.75,  # Mock quality score
                f"quality_{metric_name}_degradation": 0.15,  # Mock degradation
            }
            
            return metrics
            
        except Exception as e:
            # Return default metrics on error
            return {
                f"quality_error_{job.job_id}": 1.0,
                "error_message": str(e)
            }
    
    @staticmethod  
    def _execute_attack_job(job: EvaluationJob,
                          method_configs: Dict[str, Dict[str, Any]]) -> Dict[str, float]:
        """Execute an attack robustness job."""
        try:
            from evaluation.tools.text_editor import TextEditor
            
            # This is a simplified implementation
            # In practice, you would use the actual attack pipeline
            attack_name = job.parameters.get('attack', 'unknown')
            
            metrics = {
                f"robustness_{attack_name}_tpr": 0.70,  # Mock TPR after attack
                f"robustness_{attack_name}_fpr": 0.25,  # Mock FPR after attack
                f"robustness_{attack_name}_degradation": 0.30,  # Mock degradation
            }
            
            return metrics
            
        except Exception as e:
            # Return default metrics on error
            return {
                f"attack_error_{job.job_id}": 1.0,
                "error_message": str(e)
            }


# Configuration Management
class ConfigurationManager:
    """Manages configuration files and optimized configurations."""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.optimized_config_file = "optimized_configurations.json"
    
    def load_config(self, config_path: str) -> Optional[Dict[str, Any]]:
        """Load configuration from JSON file."""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load config {config_path}: {e}")
            return None
    
    def update_config(self, config_path: str, updates: Dict[str, Any]) -> bool:
        """Update configuration file with new parameters."""
        try:
            config = self.load_config(config_path)
            if config is None:
                return False
            
            config.update(updates)
            
            with open(config_path, 'w') as f:
                json.dump(config, f, indent=2)
            return True
        except Exception as e:
            self.logger.error(f"Failed to update config {config_path}: {e}")
            return False
    
    def save_optimized_configurations(self, configurations: List[OptimizedConfiguration]) -> bool:
        """Save optimized configurations to file."""
        try:
            config_data = {
                "configurations": [asdict(config) for config in configurations],
                "version": "1.0"
            }
            
            with open(self.optimized_config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
            
            self.logger.info(f"Saved {len(configurations)} optimized configurations")
            return True
        except Exception as e:
            self.logger.error(f"Failed to save optimized configurations: {e}")
            return False
    
    def load_optimized_configurations(self) -> List[OptimizedConfiguration]:
        """Load previously optimized configurations."""
        try:
            if not os.path.exists(self.optimized_config_file):
                self.logger.info("No existing optimized configurations found")
                return []
            
            with open(self.optimized_config_file, 'r') as f:
                data = json.load(f)
            
            configurations = []
            for config_dict in data.get("configurations", []):
                configurations.append(OptimizedConfiguration(**config_dict))
            
            self.logger.info(f"Loaded {len(configurations)} optimized configurations")
            return configurations
        except Exception as e:
            self.logger.error(f"Failed to load optimized configurations: {e}")
            return []


# Error Handling Framework
class AdvancedOptimizerError(Exception):
    """Base exception for Advanced Optimizer errors."""
    pass


class MethodDiscoveryError(AdvancedOptimizerError):
    """Error during method discovery."""
    pass


class OptimizationError(AdvancedOptimizerError):
    """Error during hyperparameter optimization."""
    pass


class CalibrationError(AdvancedOptimizerError):
    """Error during threshold calibration."""
    pass


class EvaluationError(AdvancedOptimizerError):
    """Error during evaluation execution."""
    pass


class ErrorHandler:
    """Centralized error handling and recovery."""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
    
    def handle_method_error(self, error: Exception, method_name: str) -> None:
        """Handle method-specific errors."""
        self.logger.error(f"Error with method {method_name}: {error}")
        self.logger.debug(f"Traceback: {traceback.format_exc()}")
    
    def handle_optimization_error(self, error: Exception, method_name: str, trial_number: int) -> None:
        """Handle optimization trial errors."""
        self.logger.warning(f"Optimization trial {trial_number} failed for {method_name}: {error}")
    
    def handle_evaluation_error(self, error: Exception, job_id: str) -> EvaluationResult:
        """Handle evaluation job errors and return error result."""
        self.logger.error(f"Evaluation job {job_id} failed: {error}")
        return EvaluationResult(
            job_id=job_id,
            method_name="unknown",
            metrics={},
            execution_time=0.0,
            success=False,
            error_message=str(error)
        )
    
    def handle_gpu_error(self, error: Exception, gpu_id: int) -> Optional[int]:
        """Handle GPU errors and return fallback GPU or None for CPU."""
        self.logger.warning(f"GPU {gpu_id} error: {error}, falling back to CPU")
        return None


# Logging Configuration
def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None) -> logging.Logger:
    """Set up comprehensive logging configuration."""
    
    # Create logger
    logger = logging.getLogger("advanced_optimizer")
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear any existing handlers
    logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, log_level.upper()))
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler if specified
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)  # Always debug level for file
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


# CLI Argument Parsing
def parse_arguments() -> argparse.Namespace:
    """Parse command-line arguments."""
    
    parser = argparse.ArgumentParser(
        description="Advanced Optimizer for Text Watermarking Methods",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --methods ggl,lcs --opt-trials 100
  %(prog)s --skip-optimization --eval-path custom_eval/
  %(prog)s --methods all --quality-penalty 2.0 --log-level DEBUG
        """
    )
    
    # Method selection
    parser.add_argument(
        "--methods",
        type=str,
        default="all",
        help="Comma-separated list of methods to process, or 'all' for all discovered methods"
    )
    
    # Optimization parameters
    parser.add_argument(
        "--opt-trials",
        type=int,
        default=100,
        help="Number of optimization trials per method (default: 100)"
    )
    
    parser.add_argument(
        "--quality-penalty",
        type=float,
        default=1.0,
        help="Lambda value for quality penalty in objective function (default: 1.0)"
    )
    
    parser.add_argument(
        "--skip-optimization",
        action="store_true",
        help="Skip optimization phase and use existing optimized_configurations.json"
    )
    
    # Evaluation parameters
    parser.add_argument(
        "--eval-path",
        type=str,
        default="evaluation",
        help="Path to evaluation directory (default: evaluation)"
    )
    
    parser.add_argument(
        "--max-workers",
        type=int,
        help="Maximum number of parallel workers (default: number of GPUs)"
    )
    
    # Logging and output
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--log-file",
        type=str,
        help="Log file path (optional)"
    )
    
    parser.add_argument(
        "--output-dir",
        type=str,
        default="results",
        help="Output directory for results (default: results)"
    )
    
    return parser.parse_args()


# Main Controller Class
class AdvancedOptimizer:
    """Main controller for the Advanced Optimizer system."""
    
    def __init__(self, args: argparse.Namespace):
        self.args = args
        self.logger = setup_logging(args.log_level, args.log_file)
        self.error_handler = ErrorHandler(self.logger)
        self.config_manager = ConfigurationManager(self.logger)
        
        # Initialize components
        self.method_discoverer = WatermarkMethodDiscoverer(self.logger)
        self.param_inferencer = HyperparameterInferencer(self.logger)
        self.optimizer = OptunaOptimizer(self.logger, args.quality_penalty)
        self.threshold_calibrator = ThresholdCalibrator(self.logger)
        self.job_discoverer = EvaluationJobDiscoverer(self.logger, args.eval_path)
        self.parallel_executor = ParallelEvaluationExecutor(self.logger, args.max_workers)
        
        # Results storage
        self.optimization_results: List[OptimizationResult] = []
        self.calibration_results: List[CalibrationResult] = []
        self.evaluation_results: List[EvaluationResult] = []
    
    def initialize_components(self) -> bool:
        """Initialize all system components."""
        components = [
            ("Method Discoverer", self.method_discoverer),
            ("Parameter Inferencer", self.param_inferencer),
            ("Optimizer", self.optimizer),
            ("Threshold Calibrator", self.threshold_calibrator),
            ("Job Discoverer", self.job_discoverer),
            ("Parallel Executor", self.parallel_executor)
        ]
        
        for name, component in components:
            try:
                if not component.initialize():
                    self.logger.error(f"Failed to initialize {name}")
                    return False
                self.logger.info(f"Initialized {name}")
            except Exception as e:
                self.logger.error(f"Error initializing {name}: {e}")
                return False
        
        return True
    
    def run_phase_1_optimization(self) -> bool:
        """Phase 1: Hyperparameter Discovery & Optimization."""
        self.logger.info("=== Phase 1: Hyperparameter Optimization ===")
        
        if self.args.skip_optimization:
            self.logger.info("Skipping optimization, loading existing configurations")
            optimized_configs = self.config_manager.load_optimized_configurations()
            if not optimized_configs:
                self.logger.error("No existing optimized configurations found")
                return False
            return True
        
        # Discover available methods
        discovered_methods = self.method_discoverer.discover_methods()
        if not discovered_methods:
            self.logger.error("No watermarking methods discovered")
            return False
        
        # Filter methods based on user input
        method_names = self.args.methods.split(',') if self.args.methods != 'all' else ['all']
        target_methods = self.method_discoverer.filter_methods(method_names)
        
        if not target_methods:
            self.logger.error("No valid methods found for optimization")
            return False
        
        self.logger.info(f"Optimizing {len(target_methods)} methods: {[m.name for m in target_methods]}")
        
        # Optimize each method
        for method_info in target_methods:
            try:
                self.logger.info(f"Starting optimization for {method_info.name}")
                
                # Run Optuna optimization
                optimization_result = self.optimizer.optimize_method(method_info, self.args.opt_trials)
                
                if optimization_result:
                    self.optimization_results.append(optimization_result)
                    self.logger.info(f"Optimization completed for {method_info.name}")
                else:
                    self.logger.warning(f"Optimization failed for {method_info.name}")
                    
            except Exception as e:
                self.error_handler.handle_method_error(e, method_info.name)
                continue
        
        if not self.optimization_results:
            self.logger.error("All optimizations failed")
            return False
        
        self.logger.info(f"Phase 1 completed. Optimized {len(self.optimization_results)} methods")
        return True
    
    def run_phase_2_calibration(self) -> bool:
        """Phase 2: Threshold Calibration."""
        self.logger.info("=== Phase 2: Threshold Calibration ===")
        
        if not self.optimization_results:
            self.logger.error("No optimization results available for calibration")
            return False
        
        # Calibrate threshold for each optimized method
        for opt_result in self.optimization_results:
            try:
                self.logger.info(f"Calibrating threshold for {opt_result.method_name}")
                
                # Run threshold calibration
                calibration_result = self.threshold_calibrator.calibrate_threshold(
                    opt_result.method_name, 
                    opt_result.best_params
                )
                
                if calibration_result:
                    self.calibration_results.append(calibration_result)
                    self.logger.info(f"Threshold calibration completed for {opt_result.method_name}")
                else:
                    self.logger.warning(f"Threshold calibration failed for {opt_result.method_name}")
                    
            except Exception as e:
                self.error_handler.handle_method_error(e, opt_result.method_name)
                continue
        
        if not self.calibration_results:
            self.logger.error("All threshold calibrations failed")
            return False
        
        # Save optimized configurations
        optimized_configs = []
        for opt_result, cal_result in zip(self.optimization_results, self.calibration_results):
            if opt_result.method_name == cal_result.method_name:
                config = OptimizedConfiguration(
                    method_name=opt_result.method_name,
                    hyperparameters=opt_result.best_params,
                    detection_threshold=cal_result.optimal_threshold,
                    optimization_score=opt_result.best_score,
                    calibration_metrics={
                        'roc_auc': cal_result.roc_auc,
                        'validation_tpr': cal_result.validation_tpr,
                        'validation_fpr': cal_result.validation_fpr
                    },
                    timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
                )
                optimized_configs.append(config)
        
        # Save configurations to file
        if optimized_configs:
            self.config_manager.save_optimized_configurations(optimized_configs)
            self.logger.info(f"Saved {len(optimized_configs)} optimized configurations")
        
        self.logger.info(f"Phase 2 completed. Calibrated {len(self.calibration_results)} methods")
        return True
    
    def run_phase_3_evaluation(self) -> bool:
        """Phase 3: Comprehensive Evaluation."""
        self.logger.info("=== Phase 3: Comprehensive Evaluation ===")
        
        # Load optimized configurations
        optimized_configs = self.config_manager.load_optimized_configurations()
        if not optimized_configs:
            self.logger.error("No optimized configurations available for evaluation")
            return False
        
        # Discover evaluation jobs
        evaluation_jobs = self.job_discoverer.discover_evaluation_jobs()
        if not evaluation_jobs:
            self.logger.warning("No evaluation jobs discovered")
            return True
        
        # Prepare method configurations dictionary
        method_configs = {}
        for config in optimized_configs:
            method_configs[config.method_name] = {
                **config.hyperparameters,
                'detection_threshold': config.detection_threshold
            }
        
        # Process each method sequentially with parallel job execution
        all_results = []
        for config in optimized_configs:
            method_name = config.method_name
            self.logger.info(f"Evaluating method: {method_name}")
            
            try:
                # Create method-specific jobs
                method_jobs = []
                for job in evaluation_jobs:
                    # Clone job and assign method
                    method_job = EvaluationJob(
                        job_id=f"{method_name}_{job.job_id}",
                        job_type=job.job_type,
                        job_name=f"{method_name} - {job.job_name}",
                        parameters={**job.parameters, 'method_name': method_name}
                    )
                    method_jobs.append(method_job)
                
                # Execute jobs for this method in parallel
                method_results = self.parallel_executor.execute_evaluation_jobs(
                    method_jobs, 
                    {method_name: method_configs[method_name]}
                )
                
                all_results.extend(method_results)
                
                successful_jobs = sum(1 for r in method_results if r.success)
                self.logger.info(f"Method {method_name}: {successful_jobs}/{len(method_results)} jobs succeeded")
                
            except Exception as e:
                self.error_handler.handle_method_error(e, method_name)
                continue
        
        self.evaluation_results = all_results
        
        successful_evaluations = sum(1 for r in self.evaluation_results if r.success)
        self.logger.info(f"Phase 3 completed. {successful_evaluations}/{len(self.evaluation_results)} evaluations succeeded")
        
        return True
    
    def generate_final_report(self) -> None:
        """Generate comprehensive final report."""
        self.logger.info("=== Generating Final Report ===")
        
        # Create output directory
        os.makedirs(self.args.output_dir, exist_ok=True)
        
        # Generate summary report
        report_path = os.path.join(self.args.output_dir, "optimization_report.md")
        with open(report_path, 'w') as f:
            f.write("# Advanced Optimizer Results\n\n")
            f.write(f"Generated on: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write(f"## Configuration\n")
            f.write(f"- Methods: {self.args.methods}\n")
            f.write(f"- Optimization trials: {self.args.opt_trials}\n")
            f.write(f"- Quality penalty: {self.args.quality_penalty}\n")
            f.write(f"- Skip optimization: {self.args.skip_optimization}\n")
            f.write(f"- Evaluation path: {self.args.eval_path}\n")
            f.write(f"- Output directory: {self.args.output_dir}\n\n")
            
            f.write(f"## Results Summary\n")
            f.write(f"- Optimization results: {len(self.optimization_results)}\n")
            f.write(f"- Calibration results: {len(self.calibration_results)}\n")
            f.write(f"- Evaluation results: {len(self.evaluation_results)}\n")
            f.write(f"- Successful evaluations: {sum(1 for r in self.evaluation_results if r.success)}\n\n")
            
            # Detailed optimization results
            if self.optimization_results:
                f.write("## Optimization Results\n\n")
                for result in self.optimization_results:
                    f.write(f"### {result.method_name}\n")
                    f.write(f"- Best Score: {result.best_score:.4f}\n")
                    f.write(f"- Trials: {result.n_trials}\n")
                    f.write(f"- Time: {result.optimization_time:.2f}s\n")
                    f.write(f"- Best Parameters:\n")
                    for param, value in result.best_params.items():
                        if param not in ['algorithm_name', 'model_name', 'device']:
                            f.write(f"  - {param}: {value}\n")
                    f.write("\n")
            
            # Detailed calibration results
            if self.calibration_results:
                f.write("## Threshold Calibration Results\n\n")
                for result in self.calibration_results:
                    f.write(f"### {result.method_name}\n")
                    f.write(f"- Optimal Threshold: {result.optimal_threshold:.4f}\n")
                    f.write(f"- ROC AUC: {result.roc_auc:.4f}\n")
                    f.write(f"- Validation TPR: {result.validation_tpr:.4f}\n")
                    f.write(f"- Validation FPR: {result.validation_fpr:.4f}\n")
                    f.write(f"- Calibration Time: {result.calibration_time:.2f}s\n\n")
            
            # Evaluation results summary
            if self.evaluation_results:
                f.write("## Evaluation Results\n\n")
                
                # Group results by method
                method_results = {}
                for result in self.evaluation_results:
                    method_name = result.method_name
                    if method_name not in method_results:
                        method_results[method_name] = []
                    method_results[method_name].append(result)
                
                for method_name, results in method_results.items():
                    f.write(f"### {method_name}\n")
                    successful = [r for r in results if r.success]
                    failed = [r for r in results if not r.success]
                    
                    f.write(f"- Total Jobs: {len(results)}\n")
                    f.write(f"- Successful: {len(successful)}\n")
                    f.write(f"- Failed: {len(failed)}\n")
                    
                    if successful:
                        avg_time = sum(r.execution_time for r in successful) / len(successful)
                        f.write(f"- Average Execution Time: {avg_time:.2f}s\n")
                        
                        # Show key metrics
                        all_metrics = {}
                        for result in successful:
                            for metric, value in result.metrics.items():
                                if metric not in all_metrics:
                                    all_metrics[metric] = []
                                all_metrics[metric].append(value)
                        
                        if all_metrics:
                            f.write("- Key Metrics:\n")
                            for metric, values in all_metrics.items():
                                if len(values) > 0 and all(isinstance(v, (int, float)) for v in values):
                                    avg_value = sum(values) / len(values)
                                    f.write(f"  - {metric}: {avg_value:.4f}\n")
                    
                    f.write("\n")
        
        # Generate JSON report with detailed data
        json_report_path = os.path.join(self.args.output_dir, "detailed_results.json")
        detailed_data = {
            "timestamp": time.strftime('%Y-%m-%d %H:%M:%S'),
            "configuration": {
                "methods": self.args.methods,
                "optimization_trials": self.args.opt_trials,
                "quality_penalty": self.args.quality_penalty,
                "skip_optimization": self.args.skip_optimization,
                "evaluation_path": self.args.eval_path
            },
            "optimization_results": [asdict(r) for r in self.optimization_results],
            "calibration_results": [asdict(r) for r in self.calibration_results],
            "evaluation_results": [asdict(r) for r in self.evaluation_results]
        }
        
        with open(json_report_path, 'w') as f:
            json.dump(detailed_data, f, indent=2)
        
        # Save optimized configurations separately for easy reuse
        if self.calibration_results and self.optimization_results:
            configs_path = os.path.join(self.args.output_dir, "final_optimized_configurations.json")
            final_configs = {}
            
            for opt_result in self.optimization_results:
                cal_result = next((c for c in self.calibration_results 
                                 if c.method_name == opt_result.method_name), None)
                if cal_result:
                    final_configs[opt_result.method_name] = {
                        "hyperparameters": opt_result.best_params,
                        "detection_threshold": cal_result.optimal_threshold,
                        "optimization_score": opt_result.best_score,
                        "roc_auc": cal_result.roc_auc
                    }
            
            with open(configs_path, 'w') as f:
                json.dump(final_configs, f, indent=2)
            
            self.logger.info(f"Final configurations saved: {configs_path}")
        
        self.logger.info(f"Reports generated:")
        self.logger.info(f"  - Summary: {report_path}")
        self.logger.info(f"  - Detailed: {json_report_path}")
    
    def cleanup(self) -> None:
        """Clean up system resources."""
        components = [
            self.method_discoverer,
            self.param_inferencer,
            self.optimizer,
            self.threshold_calibrator,
            self.job_discoverer,
            self.parallel_executor
        ]
        
        for component in components:
            try:
                component.cleanup()
            except Exception as e:
                self.logger.warning(f"Error during cleanup: {e}")
    
    def run(self) -> int:
        """Run the complete Advanced Optimizer workflow."""
        try:
            self.logger.info("Starting Advanced Optimizer")
            
            # Initialize all components
            if not self.initialize_components():
                self.logger.error("Component initialization failed")
                return 1
            
            # Run three-phase workflow
            if not self.run_phase_1_optimization():
                self.logger.error("Phase 1 (Optimization) failed")
                return 1
            
            if not self.run_phase_2_calibration():
                self.logger.error("Phase 2 (Calibration) failed")
                return 1
            
            if not self.run_phase_3_evaluation():
                self.logger.error("Phase 3 (Evaluation) failed")
                return 1
            
            # Generate final report
            self.generate_final_report()
            
            self.logger.info("Advanced Optimizer completed successfully")
            return 0
            
        except KeyboardInterrupt:
            self.logger.info("Interrupted by user")
            return 130
        except Exception as e:
            self.logger.error(f"Unexpected error: {e}")
            self.logger.debug(f"Traceback: {traceback.format_exc()}")
            return 1
        finally:
            self.cleanup()


def main() -> int:
    """Main entry point."""
    args = parse_arguments()
    optimizer = AdvancedOptimizer(args)
    return optimizer.run()


if __name__ == "__main__":
    sys.exit(main())