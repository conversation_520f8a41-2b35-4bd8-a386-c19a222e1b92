# Requirements Document

## Introduction

The Advanced Optimizer is a sophisticated master controller system for text watermarking benchmarks that automates discovery, optimization, and evaluation processes. This system will serve as a comprehensive solution for dynamically discovering watermarking methods from the existing MarkLLM framework structure (including GGL, LCS, and Red-Green List methods), optimizing their hyperparameters using Bayesian optimization, calibrating detection thresholds, and executing parallelized evaluations across multiple GPUs. The system must integrate with the existing evaluation framework including C4Dataset, detection pipelines, and text editing tools, while being highly extensible and data-driven without hardcoded dependencies.

## Requirements

### Requirement 1

**User Story:** As a researcher, I want the system to automatically discover all available watermarking methods from the MarkLLM framework structure, so that I don't need to manually maintain lists of methods when new ones are added.

#### Acceptance Criteria

1. WHEN the system starts THEN it SHALL scan the watermark/ directory structure and discover all available watermarking method subdirectories (e.g., ggl/, lcs/, red_green_list/)
2. WHEN a new watermarking method is added to the watermark/ directory THEN the system SHALL automatically include it in subsequent runs without code changes
3. WHEN the system discovers a method THEN it SHALL dynamically import the method class and its corresponding config class using Python's inspect module
4. WHEN the system loads a method THEN it SHALL use the AutoWatermark.load() interface with the appropriate algorithm name and config file
5. IF a method file cannot be imported THEN the system SHALL log the error and continue with other methods

### Requirement 2

**User Story:** As a researcher, I want the system to intelligently infer hyperparameter search spaces from method config classes, so that I can optimize methods without manually specifying parameter ranges.

#### Acceptance Criteria

1. WHEN analyzing a method's config class THEN the system SHALL identify all tunable parameters from the initialize_parameters() method and config_dict structure
2. WHEN a parameter has specific patterns THEN the system SHALL infer appropriate search ranges (e.g., green_list_ratio: Float(0.1, 0.9), cfg_scale: Float(1.0, 10.0), delta: Float(0.1, 5.0))
3. WHEN parameter ranges cannot be inferred THEN the system SHALL use sensible defaults based on parameter names and types (e.g., threshold parameters: Float(0.1, 1.0), scale parameters: Float(0.5, 5.0))
4. WHEN the system discovers hyperparameters THEN it SHALL store the parameter metadata including the config file path for use in optimization
5. WHEN optimizing parameters THEN the system SHALL modify the config JSON files with the trial parameters before loading the watermark

### Requirement 3

**User Story:** As a researcher, I want to optimize watermarking methods using Bayesian optimization with a balanced objective function, so that I can find configurations that maximize detection performance while minimizing quality degradation.

#### Acceptance Criteria

1. WHEN running hyperparameter optimization THEN the system SHALL use Optuna for Bayesian optimization
2. WHEN calculating the objective score THEN the system SHALL compute: TPR - (lambda * PPL_increase_ratio)
3. WHEN evaluating a configuration THEN the system SHALL use the existing evaluation framework including C4Dataset for consistent data sampling
4. WHEN evaluating detection performance THEN the system SHALL generate watermarked and unwatermarked texts and calculate TPR using the method's detect_watermark() function
5. WHEN evaluating quality THEN the system SHALL use text quality analysis tools to compute perplexity increase ratios
6. WHEN optimization completes THEN the system SHALL store the best hyperparameters for each method

### Requirement 4

**User Story:** As a researcher, I want the system to automatically calibrate detection thresholds using statistical methods, so that I can achieve optimal detection performance with the optimized hyperparameters.

#### Acceptance Criteria

1. WHEN hyperparameter optimization completes THEN the system SHALL generate detection scores on a balanced validation dataset
2. WHEN calculating optimal thresholds THEN the system SHALL use ROC curve analysis and Youden's J statistic
3. WHEN threshold calibration completes THEN the system SHALL store both hyperparameters and detection thresholds in optimized_configurations.json
4. IF threshold optimization tools are available THEN the system SHALL leverage evaluation/tools/threshold_optimizer.py

### Requirement 5

**User Story:** As a researcher, I want the system to automatically discover all available evaluation tasks from the existing MarkLLM evaluation framework, so that new evaluation methods are included without manual configuration.

#### Acceptance Criteria

1. WHEN starting evaluation phase THEN the system SHALL scan evaluation/examples/, evaluation/pipelines/, and evaluation/tools/ directories
2. WHEN discovering evaluation files THEN the system SHALL automatically generate a complete job list including detection pipelines, text editing attacks, and quality analysis tools
3. WHEN a new evaluation file is added THEN the system SHALL include it in subsequent benchmark runs
4. WHEN evaluation discovery fails THEN the system SHALL log errors and continue with available evaluations
5. WHEN running evaluations THEN the system SHALL use the existing evaluation infrastructure including WatermarkedTextDetectionPipeline and text editors

### Requirement 6

**User Story:** As a researcher, I want to run evaluations in parallel across multiple GPUs, so that I can efficiently utilize available hardware resources and reduce total execution time.

#### Acceptance Criteria

1. WHEN starting evaluations THEN the system SHALL detect available GPU devices using nvidia-smi or similar methods
2. WHEN running parallel evaluations THEN the system SHALL create a multiprocessing pool sized to match available GPUs
3. WHEN assigning jobs THEN the system SHALL distribute evaluation tasks across GPUs in round-robin fashion
4. WHEN a worker starts THEN it SHALL set CUDA_VISIBLE_DEVICES to its assigned GPU ID
5. IF GPU detection fails THEN the system SHALL fall back to CPU-based evaluation

### Requirement 7

**User Story:** As a researcher, I want a comprehensive command-line interface, so that I can control all aspects of the optimization and evaluation process.

#### Acceptance Criteria

1. WHEN using the CLI THEN the system SHALL accept --methods parameter to specify which methods to process
2. WHEN using the CLI THEN the system SHALL accept --opt-trials parameter to control optimization iterations
3. WHEN using the CLI THEN the system SHALL accept --quality-penalty parameter to set lambda value
4. WHEN using the CLI THEN the system SHALL accept --eval-path parameter to specify custom evaluation directories
5. WHEN using the CLI THEN the system SHALL accept --skip-optimization flag to bypass optimization and use existing configurations

### Requirement 8

**User Story:** As a researcher, I want the system to generate comprehensive reports and maintain persistent configurations, so that I can analyze results and reproduce experiments.

#### Acceptance Criteria

1. WHEN optimization completes THEN the system SHALL save results to optimized_configurations.json with both hyperparameters and thresholds
2. WHEN evaluations complete THEN the system SHALL generate detailed result reports in structured format
3. WHEN all processing completes THEN the system SHALL create a final comprehensive summary report
4. WHEN using --skip-optimization THEN the system SHALL load existing optimized_configurations.json
5. IF configuration files are missing THEN the system SHALL provide clear error messages and guidance

### Requirement 9

**User Story:** As a developer, I want the system to have robust error handling and logging, so that I can diagnose issues and ensure reliable operation.

#### Acceptance Criteria

1. WHEN errors occur THEN the system SHALL log detailed error messages with context
2. WHEN critical errors occur THEN the system SHALL fail gracefully with informative messages
3. WHEN GPU operations fail THEN the system SHALL fall back to CPU processing where possible
4. WHEN file operations fail THEN the system SHALL provide clear guidance on resolution
5. WHEN the system runs THEN it SHALL provide progress indicators and status updates

### Requirement 10

**User Story:** As a developer, I want the system to be modular and extensible, so that new features can be added without major refactoring.

#### Acceptance Criteria

1. WHEN implementing the system THEN it SHALL use clear separation of concerns with dedicated functions for each major task
2. WHEN adding new functionality THEN existing code SHALL not require modification due to modular design
3. WHEN the system runs THEN it SHALL use consistent interfaces between components
4. WHEN maintaining the code THEN it SHALL have comprehensive documentation and comments
5. WHEN extending the system THEN new components SHALL follow established patterns and conventions