# Implementation Plan

- [ ] 1. Set up project structure and core interfaces
  - Create advanced_optimizer.py with main entry point and CLI argument parsing
  - Define core data structures (MethodInfo, OptimizationResult, CalibrationResult, EvaluationJob, EvaluationResult)
  - Implement base classes and interfaces for all major components
  - Set up logging configuration and error handling framework
  - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5, 9.1, 9.2_

- [-] 2. Implement method discovery and configuration management
- [-] 2.1 Create WatermarkMethodDiscoverer class
  - Implement directory scanning for watermark methods in watermark/ subdirectories
  - Create method validation using AutoWatermark.load() interface
  - Implement config file loading and parsing from config/ directory
  - Add error handling for missing files and import failures
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [-] 2.2 Implement ConfigurationManager class
  - Create config file backup and restore functionality
  - Implement config file updates with parameter modifications
  - Add JSON validation and error handling for malformed configs
  - Create optimized_configurations.json persistence mechanism
  - _Requirements: 8.1, 8.4, 9.4_

- [ ] 3. Implement hyperparameter inference system
- [ ] 3.1 Create HyperparameterInferencer class
  - Implement parameter discovery from config JSON structures
  - Create intelligent search space inference based on parameter names and values
  - Add support for method-specific parameter patterns (green_list_ratio, cfg_scale, delta, etc.)
  - Implement fallback ranges for unknown parameter types
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_

- [ ] 3.2 Add parameter range definitions and validation
  - Define search ranges for common watermark parameters
  - Implement parameter type inference and validation
  - Create parameter metadata storage for optimization use
  - Add unit tests for parameter inference logic
  - _Requirements: 2.1, 2.2, 2.3_

- [ ] 4. Implement Bayesian optimization system
- [ ] 4.1 Create OptunaOptimizer class with objective function
  - Implement Optuna study creation and configuration
  - Create balanced objective function: TPR - (lambda * PPL_increase_ratio)
  - Implement trial parameter sampling and config file updates
  - Add optimization progress tracking and logging
  - _Requirements: 3.1, 3.2, 3.6_

- [ ] 4.2 Implement evaluation methods for optimization
  - Create detection performance evaluation using C4Dataset
  - Implement TPR calculation using watermark.detect_watermark() method
  - Create text quality analysis for perplexity increase ratio calculation
  - Add validation dataset management and caching
  - _Requirements: 3.3, 3.4, 3.5_

- [ ] 4.3 Add optimization result storage and analysis
  - Implement best parameter extraction and storage
  - Create optimization study persistence and loading
  - Add optimization performance metrics and reporting
  - Implement error handling for failed trials
  - _Requirements: 3.6, 9.1, 9.2_

- [ ] 5. Implement threshold calibration system
- [ ] 5.1 Create ThresholdCalibrator class
  - Implement balanced validation dataset generation
  - Create detection score collection using optimized hyperparameters
  - Implement ROC curve analysis using sklearn.metrics
  - Add Youden's J statistic calculation for optimal threshold
  - _Requirements: 4.1, 4.2, 4.3_

- [ ] 5.2 Add threshold validation and storage
  - Implement threshold performance validation on test set
  - Create calibration result storage in optimized_configurations.json
  - Add calibration metrics calculation and reporting
  - Implement integration with existing evaluation tools if available
  - _Requirements: 4.3, 4.4, 8.1_

- [ ] 6. Implement evaluation job discovery system
- [ ] 6.1 Create EvaluationJobDiscoverer class
  - Implement directory scanning for evaluation/examples/, evaluation/pipelines/, evaluation/tools/
  - Create automatic job list generation for discovered evaluation methods
  - Implement job metadata creation and validation
  - Add support for detection pipelines, text editors, and quality analyzers
  - _Requirements: 5.1, 5.2, 5.3, 5.5_

- [ ] 6.2 Add evaluation job categorization and configuration
  - Implement job type classification (detection, attack, quality)
  - Create job parameter configuration and validation
  - Add integration with existing MarkLLM evaluation infrastructure
  - Implement error handling for missing evaluation components
  - _Requirements: 5.4, 5.5_

- [ ] 7. Implement parallel execution system
- [ ] 7.1 Create GPU detection and management
  - Implement nvidia-smi parsing for GPU detection
  - Create GPU availability checking and assignment logic
  - Add GPU memory monitoring and management
  - Implement fallback to CPU execution when GPUs unavailable
  - _Requirements: 6.1, 6.2, 6.5_

- [ ] 7.2 Create ParallelEvaluationExecutor class
  - Implement multiprocessing pool creation sized to available GPUs
  - Create round-robin job assignment to GPU workers
  - Implement CUDA_VISIBLE_DEVICES setting per worker process
  - Add worker process error handling and recovery
  - _Requirements: 6.2, 6.3, 6.4_

- [ ] 7.3 Implement evaluation job execution workers
  - Create single job execution function with GPU assignment
  - Implement watermark loading with optimized configurations
  - Add evaluation pipeline execution using MarkLLM infrastructure
  - Create structured result collection and error reporting
  - _Requirements: 6.4, 9.1, 9.3_

- [ ] 8. Implement result aggregation and reporting
- [ ] 8.1 Create result collection and aggregation system
  - Implement evaluation result gathering from parallel workers
  - Create method-wise result aggregation and analysis
  - Add performance metrics calculation and summary generation
  - Implement result validation and error detection
  - _Requirements: 8.2, 8.3_

- [ ] 8.2 Add comprehensive reporting system
  - Create detailed JSON result reports with structured data
  - Implement summary report generation in Markdown format
  - Add performance comparison across methods and configurations
  - Create visualization data preparation for analysis tools
  - _Requirements: 8.2, 8.3_

- [ ] 9. Implement CLI interface and workflow orchestration
- [ ] 9.1 Create command-line argument parsing
  - Implement argparse interface with all required parameters
  - Add parameter validation and help documentation
  - Create configuration loading from CLI arguments
  - Implement workflow control flags (--skip-optimization)
  - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5_

- [ ] 9.2 Implement main workflow orchestration
  - Create three-phase workflow execution (optimization, calibration, evaluation)
  - Implement phase skipping and resume functionality
  - Add progress tracking and status reporting throughout execution
  - Create workflow state persistence and recovery
  - _Requirements: 8.4, 9.5_

- [ ] 10. Add comprehensive error handling and logging
- [ ] 10.1 Implement error handling framework
  - Create error categories and handling strategies for each component
  - Implement graceful degradation for non-critical failures
  - Add error recovery mechanisms for GPU and memory issues
  - Create detailed error logging with context information
  - _Requirements: 9.1, 9.2, 9.3, 9.4_

- [ ] 10.2 Add logging and monitoring system
  - Implement comprehensive logging throughout all components
  - Create progress indicators and status updates for long-running operations
  - Add performance monitoring and resource usage tracking
  - Implement debug mode with detailed execution tracing
  - _Requirements: 9.5, 10.4_

- [ ] 11. Implement configuration persistence and loading
- [ ] 11.1 Add optimized configuration management
  - Create optimized_configurations.json structure and schema
  - Implement configuration loading for --skip-optimization mode
  - Add configuration validation and migration support
  - Create configuration backup and versioning system
  - _Requirements: 8.1, 8.4_

- [ ] 11.2 Add integration testing and validation
  - Create end-to-end workflow tests with mock data
  - Implement integration tests with MarkLLM framework components
  - Add performance benchmarks and resource usage validation
  - Create test data generation and cleanup utilities
  - _Requirements: 10.1, 10.2, 10.3, 10.5_

- [ ] 12. Final integration and optimization
- [ ] 12.1 Integrate all components into main script
  - Wire together all components in the main advanced_optimizer.py script
  - Implement proper initialization order and dependency management
  - Add final error handling and cleanup procedures
  - Create comprehensive documentation and usage examples
  - _Requirements: 10.1, 10.2, 10.4, 10.5_

- [ ] 12.2 Performance optimization and testing
  - Optimize memory usage and execution speed across all components
  - Add caching mechanisms for expensive operations
  - Implement resource cleanup and garbage collection
  - Create final validation tests with real MarkLLM methods
  - _Requirements: 10.3, 10.4, 10.5_