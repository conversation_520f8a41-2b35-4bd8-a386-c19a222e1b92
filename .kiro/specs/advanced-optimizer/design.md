# Design Document

## Overview

The Advanced Optimizer is a sophisticated three-phase system designed to automatically discover, optimize, and evaluate text watermarking methods within the MarkLLM framework. The system integrates seamlessly with the existing infrastructure including AutoWatermark interface, evaluation pipelines, and configuration management while providing intelligent hyperparameter optimization and comprehensive benchmarking capabilities.

The system operates through three distinct phases:
1. **Hyperparameter Discovery & Optimization**: Uses Bayesian optimization to find optimal configurations
2. **Threshold Calibration**: Statistically determines optimal detection thresholds using ROC analysis
3. **Comprehensive Evaluation**: Executes parallelized evaluations across multiple GPUs

## Architecture

### High-Level System Architecture

```mermaid
graph TB
    CLI[Command Line Interface] --> Main[Main Controller]
    Main --> Phase1[Phase 1: Optimization]
    Main --> Phase2[Phase 2: Calibration]
    Main --> Phase3[Phase 3: Evaluation]
    
    Phase1 --> Discovery[Method Discovery]
    Phase1 --> ParamInfer[Parameter Inference]
    Phase1 --> BayesOpt[Bayesian Optimization]
    
    Phase2 --> ThresholdCalc[Threshold Calculation]
    Phase2 --> ROCAnalysis[ROC Analysis]
    
    Phase3 --> JobDiscovery[Evaluation Job Discovery]
    Phase3 --> ParallelExec[Parallel Execution]
    Phase3 --> ResultAgg[Result Aggregation]
    
    Discovery --> MarkLLM[MarkLLM Framework]
    BayesOpt --> MarkLLM
    ParallelExec --> MarkLLM
    
    subgraph MarkLLM Framework
        AutoWM[AutoWatermark]
        Configs[Config Files]
        EvalTools[Evaluation Tools]
        Datasets[Datasets]
    end
```

### Core Components Architecture

```mermaid
graph LR
    subgraph Core System
        MC[MethodDiscoverer]
        PI[ParameterInferencer]
        OO[OptunaOptimizer]
        TC[ThresholdCalibrator]
        ED[EvaluationDiscoverer]
        PE[ParallelExecutor]
    end
    
    subgraph MarkLLM Integration
        AW[AutoWatermark Interface]
        CF[Config File Manager]
        EP[Evaluation Pipelines]
        DS[Dataset Manager]
    end
    
    MC --> AW
    PI --> CF
    OO --> AW
    TC --> EP
    ED --> EP
    PE --> DS
```

## Components and Interfaces

### 1. Method Discovery Component

**Class: `WatermarkMethodDiscoverer`**

```python
class WatermarkMethodDiscoverer:
    def discover_methods(self, watermark_dir: str) -> List[MethodInfo]
    def load_method_config(self, method_name: str) -> Dict[str, Any]
    def validate_method(self, method_info: MethodInfo) -> bool
```

**Responsibilities:**
- Scan watermark/ directory for available methods (ggl/, lcs/, red_green_list/, etc.)
- Load and validate corresponding config files from config/ directory
- Create MethodInfo objects containing method metadata and config paths
- Handle import errors gracefully and log issues

**Integration Points:**
- Uses AutoWatermark.load() to validate method loading
- Reads JSON config files from config/ directory
- Integrates with TransformersConfig for model setup

### 2. Parameter Inference Component

**Class: `HyperparameterInferencer`**

```python
class HyperparameterInferencer:
    def infer_search_space(self, config_dict: Dict[str, Any]) -> Dict[str, optuna.distributions.BaseDistribution]
    def create_parameter_ranges(self, param_name: str, param_value: Any) -> optuna.distributions.BaseDistribution
    def get_optimization_parameters(self) -> List[str]
```

**Parameter Inference Rules:**
- `green_list_ratio`: Float(0.05, 0.5) - Controls green list size
- `cfg_scale`: Float(1.0, 10.0) - Classifier-free guidance scale
- `delta`/`guidance_delta`: Float(0.5, 5.0) - Guidance strength
- `detection_threshold`: Float(0.1, 0.9) - Detection threshold
- `gen_length`: IntUniform(64, 256) - Generation length
- `steps`: IntUniform(64, 256) - Number of generation steps
- `seeding_strategy`: Categorical(['simple', 'semantic', 'advanced']) - For LCS
- `num_constraints`: IntUniform(1, 5) - For LCS constraints

### 3. Bayesian Optimization Component

**Class: `OptunaOptimizer`**

```python
class OptunaOptimizer:
    def __init__(self, quality_penalty: float = 1.0)
    def create_objective(self, method_name: str, config_path: str) -> Callable
    def optimize_method(self, method_info: MethodInfo, n_trials: int) -> optuna.study.Study
    def calculate_objective_score(self, tpr: float, ppl_ratio: float) -> float
```

**Objective Function Design:**
```python
def objective_function(trial):
    # Sample hyperparameters
    params = self.sample_parameters(trial)
    
    # Update config file with trial parameters
    self.update_config(params)
    
    # Load watermark with trial configuration
    watermark = AutoWatermark.load(method_name, config_path, transformers_config)
    
    # Evaluate on validation set
    tpr = self.evaluate_detection_performance(watermark)
    ppl_ratio = self.evaluate_quality_degradation(watermark)
    
    # Return balanced objective
    return tpr - (self.quality_penalty * ppl_ratio)
```

### 4. Threshold Calibration Component

**Class: `ThresholdCalibrator`**

```python
class ThresholdCalibrator:
    def calibrate_threshold(self, watermark: BaseWatermark, validation_data: List[str]) -> float
    def generate_detection_scores(self, watermark: BaseWatermark, texts: List[str]) -> Tuple[List[float], List[bool]]
    def calculate_optimal_threshold(self, scores: List[float], labels: List[bool]) -> float
```

**Calibration Process:**
1. Generate balanced validation set (50% watermarked, 50% unwatermarked)
2. Collect detection scores using watermark.detect_watermark()
3. Compute ROC curve using sklearn.metrics.roc_curve
4. Find optimal threshold using Youden's J statistic (TPR - FPR)
5. Validate threshold performance on held-out test set

### 5. Evaluation Discovery Component

**Class: `EvaluationJobDiscoverer`**

```python
class EvaluationJobDiscoverer:
    def discover_evaluation_jobs(self, eval_path: str) -> List[EvaluationJob]
    def scan_attack_methods(self, attacks_path: str) -> List[AttackJob]
    def scan_quality_metrics(self, quality_path: str) -> List[QualityJob]
    def create_job_metadata(self, job_type: str, job_name: str) -> EvaluationJob
```

**Job Types:**
- **Detection Jobs**: Use WatermarkedTextDetectionPipeline and UnWatermarkedTextDetectionPipeline
- **Attack Jobs**: Apply text editors (TruncatePromptTextEditor, WordDeletion, etc.)
- **Quality Jobs**: Run text quality analyzers (perplexity, BERT score, etc.)
- **Robustness Jobs**: Test against various text transformations

### 6. Parallel Execution Component

**Class: `ParallelEvaluationExecutor`**

```python
class ParallelEvaluationExecutor:
    def __init__(self, max_workers: int = None)
    def get_available_gpus(self) -> List[int]
    def execute_evaluation_jobs(self, jobs: List[EvaluationJob], method_config: Dict) -> List[EvaluationResult]
    def run_single_job(self, job: EvaluationJob, gpu_id: int, method_config: Dict) -> EvaluationResult
```

**Execution Strategy:**
- Detect available GPUs using nvidia-smi parsing
- Create multiprocessing pool sized to available GPUs
- Assign jobs to workers in round-robin fashion
- Set CUDA_VISIBLE_DEVICES per worker process
- Handle GPU memory management and error recovery

## Data Models

### Core Data Structures

```python
@dataclass
class MethodInfo:
    name: str
    config_path: str
    algorithm_class: str
    parameters: Dict[str, Any]
    search_space: Dict[str, optuna.distributions.BaseDistribution]

@dataclass
class OptimizationResult:
    method_name: str
    best_params: Dict[str, Any]
    best_score: float
    study: optuna.study.Study
    optimization_time: float

@dataclass
class CalibrationResult:
    method_name: str
    optimal_threshold: float
    roc_auc: float
    validation_tpr: float
    validation_fpr: float

@dataclass
class EvaluationJob:
    job_id: str
    job_type: str  # 'detection', 'attack', 'quality'
    job_name: str
    parameters: Dict[str, Any]
    gpu_assignment: int

@dataclass
class EvaluationResult:
    job_id: str
    method_name: str
    metrics: Dict[str, float]
    execution_time: float
    success: bool
    error_message: Optional[str]

@dataclass
class OptimizedConfiguration:
    method_name: str
    hyperparameters: Dict[str, Any]
    detection_threshold: float
    optimization_score: float
    calibration_metrics: Dict[str, float]
```

### Configuration Management

```python
class ConfigurationManager:
    def load_config(self, config_path: str) -> Dict[str, Any]
    def update_config(self, config_path: str, updates: Dict[str, Any]) -> None
    def backup_config(self, config_path: str) -> str
    def restore_config(self, config_path: str, backup_path: str) -> None
    def save_optimized_configurations(self, results: List[OptimizedConfiguration]) -> None
```

## Error Handling

### Error Categories and Strategies

1. **Method Discovery Errors**
   - Missing config files: Log warning, skip method
   - Import failures: Log error with traceback, continue
   - Invalid method structure: Validate and report issues

2. **Optimization Errors**
   - Trial failures: Log and continue with next trial
   - GPU memory errors: Reduce batch size, retry
   - Model loading errors: Skip method, report issue

3. **Evaluation Errors**
   - GPU unavailability: Fall back to CPU execution
   - Dataset loading errors: Use alternative datasets
   - Pipeline failures: Log error, continue with other jobs

4. **Configuration Errors**
   - Invalid parameters: Use default values, log warning
   - File permission errors: Provide clear guidance
   - JSON parsing errors: Validate and report syntax issues

### Error Recovery Mechanisms

```python
class ErrorHandler:
    def handle_optimization_error(self, error: Exception, trial: optuna.trial.Trial) -> None
    def handle_evaluation_error(self, error: Exception, job: EvaluationJob) -> EvaluationResult
    def handle_gpu_error(self, error: Exception, gpu_id: int) -> int  # Returns fallback GPU
    def log_error_with_context(self, error: Exception, context: Dict[str, Any]) -> None
```

## Testing Strategy

### Unit Testing

1. **Method Discovery Tests**
   - Test directory scanning with mock filesystem
   - Validate config file parsing
   - Test error handling for missing files

2. **Parameter Inference Tests**
   - Test search space generation for different parameter types
   - Validate range inference logic
   - Test edge cases and invalid parameters

3. **Optimization Tests**
   - Test objective function calculation
   - Mock Optuna studies for reproducible testing
   - Test configuration updates and rollbacks

4. **Evaluation Tests**
   - Test job discovery and creation
   - Mock GPU detection and assignment
   - Test parallel execution with controlled workers

### Integration Testing

1. **End-to-End Workflow Tests**
   - Test complete optimization pipeline
   - Validate configuration persistence
   - Test evaluation result aggregation

2. **MarkLLM Integration Tests**
   - Test AutoWatermark interface compatibility
   - Validate evaluation pipeline integration
   - Test dataset loading and processing

3. **Performance Tests**
   - Benchmark optimization speed
   - Test memory usage under load
   - Validate GPU utilization efficiency

### Test Data and Mocking

```python
class TestDataManager:
    def create_mock_methods(self) -> List[MethodInfo]
    def create_sample_configs(self) -> Dict[str, Dict[str, Any]]
    def generate_validation_dataset(self, size: int) -> List[str]
    def mock_gpu_environment(self, num_gpus: int) -> None
```

## Performance Considerations

### Optimization Performance
- Use TPE (Tree-structured Parzen Estimator) sampler for efficient search
- Implement early stopping for poor-performing trials
- Cache evaluation results to avoid redundant computations
- Use warm-start strategies for related optimization runs

### Memory Management
- Implement model unloading between trials
- Use gradient checkpointing for large models
- Monitor GPU memory usage and implement cleanup
- Batch evaluation jobs to optimize memory usage

### Parallel Execution Optimization
- Implement dynamic load balancing across GPUs
- Use shared memory for large datasets
- Implement job queuing and priority scheduling
- Monitor and handle GPU failures gracefully

## Extensibility Design

### Plugin Architecture
- Define interfaces for new watermark methods
- Support custom evaluation metrics
- Allow custom optimization objectives
- Enable custom threshold calibration strategies

### Configuration Extensibility
- Support custom parameter search spaces
- Allow method-specific optimization strategies
- Enable custom evaluation job types
- Support external evaluation tools integration