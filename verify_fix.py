#!/usr/bin/env python3
"""
Verification script that outputs to file
"""

import sys
import os
sys.path.insert(0, '/mnt/newhome/kasra/markllm')

def main():
    """Test method discovery and write results to file."""
    try:
        with open('/mnt/newhome/kasra/markllm/test_results.txt', 'w') as f:
            f.write("Testing Advanced Optimizer Method Discovery Fix\n")
            f.write("=" * 50 + "\n\n")
            
            from advanced_optimizer import WatermarkMethodDiscoverer, setup_logging
            
            logger = setup_logging('INFO')
            discoverer = WatermarkMethodDiscoverer(logger)
            
            f.write("1. Testing discoverer initialization...\n")
            if discoverer.initialize():
                f.write("   ✓ Initialization successful\n\n")
                
                f.write("2. Testing method discovery...\n")
                methods = discoverer.discover_methods()
                f.write(f"   Found {len(methods)} methods:\n")
                
                for method in methods:
                    f.write(f"   - {method.name}\n")
                    f.write(f"     Config: {method.config_path}\n")
                    f.write(f"     Class: {method.algorithm_class}\n\n")
                
                expected_methods = {'GGL', 'LCS', 'RedGreenList'}
                found_methods = {method.name for method in methods}
                
                f.write("3. Verification:\n")
                f.write(f"   Expected: {expected_methods}\n")
                f.write(f"   Found: {found_methods}\n")
                
                if found_methods == expected_methods:
                    f.write("   ✅ SUCCESS: Found exactly the expected methods!\n")
                    return True
                else:
                    f.write("   ❌ FAIL: Method mismatch\n")
                    return False
            else:
                f.write("   ❌ FAIL: Initialization failed\n")
                return False
                
    except Exception as e:
        with open('/mnt/newhome/kasra/markllm/test_results.txt', 'a') as f:
            f.write(f"\n❌ ERROR: {e}\n")
            import traceback
            f.write(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    with open('/mnt/newhome/kasra/markllm/test_results.txt', 'a') as f:
        f.write(f"\nTest Result: {'PASSED' if success else 'FAILED'}\n")
    print(f"Test {'PASSED' if success else 'FAILED'} - see test_results.txt for details")
