#!/usr/bin/env python3
"""
Minimal test for method discovery fix
"""

import sys
import os
sys.path.insert(0, '/mnt/newhome/kasra/markllm')

def test_method_discovery():
    """Test that method discovery only finds the three methods we want."""
    try:
        from advanced_optimizer import WatermarkMethodDiscoverer, setup_logging
        
        logger = setup_logging('INFO')
        discoverer = WatermarkMethodDiscoverer(logger)
        
        print("Testing method discovery...")
        
        if discoverer.initialize():
            print("✓ Discoverer initialized successfully")
            
            methods = discoverer.discover_methods()
            print(f"✓ Discovery completed. Found {len(methods)} methods:")
            
            expected_methods = {'GGL', 'LCS', 'RedGreenList'}
            found_methods = {method.name for method in methods}
            
            for method in methods:
                print(f"  - {method.name}: {method.config_path}")
            
            if found_methods == expected_methods:
                print("✅ SUCCESS: Found exactly the expected methods!")
                return True
            else:
                print(f"❌ FAIL: Expected {expected_methods}, found {found_methods}")
                return False
        else:
            print("❌ FAIL: Discoverer initialization failed")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_method_discovery()
    sys.exit(0 if success else 1)
