#!/usr/bin/env python3
"""
Test script for the Advanced Optimizer
"""

import sys
import os

def test_import():
    """Test that the advanced optimizer can be imported."""
    try:
        from advanced_optimizer import AdvancedOptimizer, parse_arguments
        print("✓ Advanced Optimizer imports successfully")
        return True
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False

def test_argument_parsing():
    """Test argument parsing."""
    try:
        from advanced_optimizer import parse_arguments
        
        # Test default arguments
        sys.argv = ['test_script']
        args = parse_arguments()
        
        print(f"✓ Default arguments parsed:")
        print(f"  - methods: {args.methods}")
        print(f"  - opt_trials: {args.opt_trials}")
        print(f"  - quality_penalty: {args.quality_penalty}")
        print(f"  - log_level: {args.log_level}")
        
        return True
    except Exception as e:
        print(f"✗ Argument parsing failed: {e}")
        return False

def test_component_initialization():
    """Test component initialization."""
    try:
        from advanced_optimizer import (
            WatermarkMethodDiscoverer, 
            HyperparameterInferencer,
            EvaluationJobDiscoverer,
            ParallelEvaluationExecutor,
            setup_logging
        )
        
        logger = setup_logging("INFO")
        
        # Test method discoverer
        discoverer = WatermarkMethodDiscoverer(logger)
        init_success = discoverer.initialize()
        print(f"✓ Method discoverer initialization: {init_success}")
        
        # Test parameter inferencer
        inferencer = HyperparameterInferencer(logger)
        init_success = inferencer.initialize()
        print(f"✓ Parameter inferencer initialization: {init_success}")
        
        # Test job discoverer
        job_discoverer = EvaluationJobDiscoverer(logger)
        init_success = job_discoverer.initialize()
        print(f"✓ Job discoverer initialization: {init_success}")
        
        # Test parallel executor
        executor = ParallelEvaluationExecutor(logger)
        init_success = executor.initialize()
        print(f"✓ Parallel executor initialization: {init_success}")
        print(f"  - Available GPUs: {executor.available_gpus}")
        
        return True
    except Exception as e:
        print(f"✗ Component initialization failed: {e}")
        return False

def test_method_discovery():
    """Test method discovery functionality."""
    try:
        from advanced_optimizer import WatermarkMethodDiscoverer, setup_logging
        
        logger = setup_logging("INFO")
        discoverer = WatermarkMethodDiscoverer(logger)
        
        if discoverer.initialize():
            methods = discoverer.discover_methods()
            print(f"✓ Method discovery completed:")
            print(f"  - Found {len(methods)} methods")
            for method in methods[:3]:  # Show first 3
                print(f"    - {method.name}: {method.algorithm_class}")
            if len(methods) > 3:
                print(f"    - ... and {len(methods) - 3} more")
        else:
            print("✗ Method discoverer initialization failed")
            
        return True
    except Exception as e:
        print(f"✗ Method discovery failed: {e}")
        return False

def test_job_discovery():
    """Test evaluation job discovery."""
    try:
        from advanced_optimizer import EvaluationJobDiscoverer, setup_logging
        
        logger = setup_logging("INFO")
        job_discoverer = EvaluationJobDiscoverer(logger)
        
        if job_discoverer.initialize():
            jobs = job_discoverer.discover_evaluation_jobs()
            print(f"✓ Job discovery completed:")
            print(f"  - Found {len(jobs)} evaluation jobs")
            
            job_types = {}
            for job in jobs:
                if job.job_type not in job_types:
                    job_types[job.job_type] = 0
                job_types[job.job_type] += 1
            
            for job_type, count in job_types.items():
                print(f"    - {job_type}: {count} jobs")
        else:
            print("✗ Job discoverer initialization failed")
            
        return True
    except Exception as e:
        print(f"✗ Job discovery failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing Advanced Optimizer Implementation")
    print("=" * 50)
    
    tests = [
        test_import,
        test_argument_parsing,
        test_component_initialization,
        test_method_discovery,
        test_job_discovery
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print(f"\nRunning {test.__name__}...")
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Advanced Optimizer is ready.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
