#!/usr/bin/env python3
"""
Comprehensive LCS Integration Test for MarkLLM

This script tests the complete integration of Latent Constraint Seeding (LCS)
watermarking into the MarkLLM framework.
"""

import sys
import os
import torch
from datetime import datetime

# Add MarkLLM to path
sys.path.append('/mnt/newhome/kasra/MarkLLM')

def test_lcs_import():
    """Test if LCS can be imported correctly."""
    print("="*60)
    print("Testing LCS Import...")
    try:
        from watermark.lcs import LCS, LCSConfig
        print("✅ Successfully imported LCS and LCSConfig")
        return True
    except Exception as e:
        print(f"❌ Failed to import LCS: {e}")
        return False

def test_auto_watermark_integration():
    """Test if LCS is properly integrated into AutoWatermark."""
    print("\nTesting AutoWatermark Integration...")
    try:
        from watermark.auto_watermark import AutoWatermark
        from utils.transformers_config import TransformersConfig
        from transformers import AutoTokenizer
        
        # Load tokenizer
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        tokenizer = AutoTokenizer.from_pretrained("GSAI-ML/LLaDA-8B-Instruct", trust_remote_code=True)
        
        # Create transformers config
        transformers_config = TransformersConfig(
            model=None,  # Will be loaded by LCS
            tokenizer=tokenizer,
            vocab_size=tokenizer.vocab_size,
            device=device
        )
        
        # Load LCS through AutoWatermark
        lcs_watermark = AutoWatermark.load(
            algorithm_name="LCS",
            algorithm_config="config/LCS.json",
            transformers_config=transformers_config
        )
        
        print("✅ Successfully loaded LCS through AutoWatermark")
        print(f"   Algorithm name: {lcs_watermark.config.algorithm_name}")
        print(f"   Model name: {lcs_watermark.config.model_name}")
        print(f"   Seeding strategy: {lcs_watermark.config.seeding_strategy}")
        return True, lcs_watermark
        
    except Exception as e:
        print(f"❌ Failed to load LCS through AutoWatermark: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_lcs_generation(lcs_watermark):
    """Test LCS text generation."""
    print("\nTesting LCS Text Generation...")
    try:
        prompt = "In a galaxy far, far away, a lone explorer found a derelict starship. Inside, the only clue to its fate was a single, cryptic message on the main screen:"
        
        print(f"Prompt: {prompt}")
        print("Generating watermarked text...")
        
        watermarked_text = lcs_watermark.generate_watermarked_text(prompt)
        
        print("✅ Successfully generated watermarked text")
        print(f"Generated text: {watermarked_text[:200]}...")
        return True, watermarked_text, prompt
        
    except Exception as e:
        print(f"❌ Failed to generate watermarked text: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None

def test_lcs_detection(lcs_watermark, watermarked_text, prompt):
    """Test LCS watermark detection."""
    print("\nTesting LCS Watermark Detection...")
    try:
        survival_rate = lcs_watermark.detect_watermark(watermarked_text, prompt)
        
        print("✅ Successfully detected watermark")
        print(f"Survival rate: {survival_rate:.1%}")
        
        # Test with non-watermarked text (should have low survival rate)
        non_watermarked = "This is a regular text without any watermark embedded in it."
        non_watermarked_rate = lcs_watermark.detect_watermark(non_watermarked, prompt)
        
        print(f"Non-watermarked text survival rate: {non_watermarked_rate:.1%}")
        
        return True, survival_rate
        
    except Exception as e:
        print(f"❌ Failed to detect watermark: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_lcs_visualization(lcs_watermark, watermarked_text, prompt):
    """Test LCS visualization data generation."""
    print("\nTesting LCS Visualization...")
    try:
        viz_data = lcs_watermark.get_data_for_visualization(watermarked_text, prompt)
        
        print("✅ Successfully generated visualization data")
        print(f"Number of tokens: {len(viz_data.decoded_tokens)}")
        print(f"Number of highlights: {sum(viz_data.highlight_values)}")
        print(f"Highlighted positions: {[i for i, val in enumerate(viz_data.highlight_values) if val == 1]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to generate visualization data: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_strategies():
    """Test both semantic and cryptographic seeding strategies."""
    print("\nTesting Different Seeding Strategies...")
    
    try:
        from utils.transformers_config import TransformersConfig
        from watermark.auto_watermark import AutoWatermark
        from transformers import AutoTokenizer
        
        # Load tokenizer
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        tokenizer = AutoTokenizer.from_pretrained("GSAI-ML/LLaDA-8B-Instruct", trust_remote_code=True)
        
        transformers_config = TransformersConfig(
            model=None,
            tokenizer=tokenizer,
            vocab_size=tokenizer.vocab_size,
            device=device
        )
        
        prompt = "Write a short story about a robot learning to paint."
        
        # Test semantic strategy (default)
        print("Testing semantic strategy...")
        lcs_semantic = AutoWatermark.load("LCS", "config/LCS.json", transformers_config)
        semantic_text = lcs_semantic.generate_watermarked_text(prompt)
        semantic_rate = lcs_semantic.detect_watermark(semantic_text, prompt)
        
        print(f"✅ Semantic strategy - Survival rate: {semantic_rate:.1%}")
        
        # Test cryptographic strategy
        print("Testing cryptographic strategy...")
        # Create a temporary config for cryptographic strategy
        lcs_semantic.config.seeding_strategy = 'cryptographic'
        crypto_text = lcs_semantic.generate_watermarked_text(prompt)
        crypto_rate = lcs_semantic.detect_watermark(crypto_text, prompt)
        
        print(f"✅ Cryptographic strategy - Survival rate: {crypto_rate:.1%}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to test different strategies: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run comprehensive LCS integration tests."""
    print("="*60)
    print("         LCS INTEGRATION TEST FOR MARKLLM")
    print("="*60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Device: {'CUDA' if torch.cuda.is_available() else 'CPU'}")
    
    tests_passed = 0
    total_tests = 6
    
    # Test 1: Import
    if test_lcs_import():
        tests_passed += 1
    
    # Test 2: AutoWatermark integration
    success, lcs_watermark = test_auto_watermark_integration()
    if success:
        tests_passed += 1
        
        # Test 3: Generation
        success, watermarked_text, prompt = test_lcs_generation(lcs_watermark)
        if success:
            tests_passed += 1
            
            # Test 4: Detection
            success, survival_rate = test_lcs_detection(lcs_watermark, watermarked_text, prompt)
            if success:
                tests_passed += 1
                
                # Test 5: Visualization
                if test_lcs_visualization(lcs_watermark, watermarked_text, prompt):
                    tests_passed += 1
    
    # Test 6: Different strategies
    if test_different_strategies():
        tests_passed += 1
    
    # Final results
    print("\n" + "="*60)
    print("                  FINAL RESULTS")
    print("="*60)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 ALL TESTS PASSED! LCS is successfully integrated into MarkLLM!")
    elif tests_passed >= total_tests - 1:
        print("✅ LCS integration mostly successful with minor issues.")
    else:
        print("⚠️ LCS integration has significant issues that need to be addressed.")
    
    print("="*60)
    
    # Save test results
    results = {
        'timestamp': datetime.now().isoformat(),
        'tests_passed': tests_passed,
        'total_tests': total_tests,
        'success_rate': tests_passed / total_tests,
        'device': 'cuda' if torch.cuda.is_available() else 'cpu'
    }
    
    import json
    with open('lcs_integration_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"Test results saved to: lcs_integration_test_results.json")
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
