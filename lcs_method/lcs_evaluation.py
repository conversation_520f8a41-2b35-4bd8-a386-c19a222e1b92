#!/usr/bin/env python3
"""
LCS Watermarking Evaluation for MarkLLM

This script provides comprehensive evaluation of the Latent Constraint Seeding (LCS)
watermarking method integrated into MarkLLM.
"""

import sys
import os
import torch
import json
import numpy as np
from datetime import datetime
from typing import List, Dict, Any

# Add MarkLLM to path
sys.path.append('/mnt/newhome/kasra/MarkLLM')

def load_evaluation_prompts() -> List[str]:
    """Load a diverse set of prompts for evaluation."""
    return [
        "In a galaxy far, far away, a lone explorer found a derelict starship. Inside, the only clue to its fate was a single, cryptic message on the main screen:",
        "Write a short story about a robot who discovers music for the first time.",
        "Explain the concept of artificial intelligence to a child using simple analogies.",
        "Describe a day in the life of a person living in a city on Mars in the year 2150.",
        "Write a persuasive essay about the importance of reading books in the digital age.",
        "Tell me about the most interesting historical event you can think of and why it matters.",
        "Create a dialogue between two characters meeting for the first time at a coffee shop.",
        "Explain how photosynthesis works as if you're teaching a high school biology class.",
        "Write a poem about the changing seasons and the passage of time.",
        "Describe your ideal vacation destination and what makes it special.",
        "Write a mystery story set in a small town library.",
        "Explain the concept of blockchain technology in simple terms.",
        "Create a character description for a protagonist in a fantasy novel.",
        "Write about a scientific discovery that could change the world.",
        "Describe a futuristic transportation system and how it works.",
        "Write a letter from a grandparent to their grandchild giving life advice.",
        "Explain the water cycle as if you're narrating a nature documentary.",
        "Create a story about someone who finds a mysterious key.",
        "Write about the importance of teamwork using examples from sports or nature.",
        "Describe what life might be like if humans could communicate telepathically."
    ]

def evaluate_basic_performance(lcs_watermark, prompts: List[str]) -> Dict[str, Any]:
    """Evaluate basic LCS performance on generation and detection."""
    results = {
        'total_prompts': len(prompts),
        'successful_generations': 0,
        'survival_rates': [],
        'generation_errors': [],
        'detection_errors': [],
        'texts': []
    }
    
    print(f"Evaluating basic performance on {len(prompts)} prompts...")
    
    for i, prompt in enumerate(prompts):
        try:
            # Generate watermarked text
            watermarked_text = lcs_watermark.generate_watermarked_text(prompt)
            
            # Detect watermark
            survival_rate = lcs_watermark.detect_watermark(watermarked_text, prompt)
            
            results['successful_generations'] += 1
            results['survival_rates'].append(survival_rate)
            results['texts'].append({
                'prompt': prompt,
                'text': watermarked_text,
                'survival_rate': survival_rate
            })
            
            print(f"  {i+1:2d}/{len(prompts)} - Survival: {survival_rate:.1%}")
            
        except Exception as e:
            error_info = {'prompt_index': i, 'prompt': prompt, 'error': str(e)}
            if 'generate' in str(e).lower():
                results['generation_errors'].append(error_info)
            else:
                results['detection_errors'].append(error_info)
            print(f"  {i+1:2d}/{len(prompts)} - ERROR: {str(e)[:50]}...")
    
    # Calculate statistics
    if results['survival_rates']:
        results['avg_survival_rate'] = np.mean(results['survival_rates'])
        results['std_survival_rate'] = np.std(results['survival_rates'])
        results['min_survival_rate'] = np.min(results['survival_rates'])
        results['max_survival_rate'] = np.max(results['survival_rates'])
        results['success_rate'] = sum(1 for rate in results['survival_rates'] if rate > 0.5) / len(results['survival_rates'])
    else:
        results['avg_survival_rate'] = 0.0
        results['std_survival_rate'] = 0.0
        results['min_survival_rate'] = 0.0
        results['max_survival_rate'] = 0.0
        results['success_rate'] = 0.0
    
    return results

def evaluate_strategy_comparison(transformers_config) -> Dict[str, Any]:
    """Compare semantic vs cryptographic seeding strategies."""
    from watermark.auto_watermark import AutoWatermark
    
    results = {'semantic': {}, 'cryptographic': {}}
    test_prompts = load_evaluation_prompts()[:5]  # Use subset for comparison
    
    print("Comparing seeding strategies...")
    
    for strategy in ['semantic', 'cryptographic']:
        print(f"\nEvaluating {strategy} strategy...")
        
        try:
            # Load watermark with specific strategy
            lcs_watermark = AutoWatermark.load("LCS", "config/LCS.json", transformers_config)
            lcs_watermark.config.seeding_strategy = strategy
            
            strategy_results = evaluate_basic_performance(lcs_watermark, test_prompts)
            results[strategy] = strategy_results
            
        except Exception as e:
            print(f"Error evaluating {strategy} strategy: {e}")
            results[strategy] = {'error': str(e)}
    
    return results

def evaluate_robustness(lcs_watermark, test_texts: List[Dict]) -> Dict[str, Any]:
    """Evaluate robustness against various attacks."""
    results = {
        'paraphrasing': [],
        'synonym_replacement': [],
        'text_truncation': [],
        'text_addition': []
    }
    
    print("Evaluating robustness against attacks...")
    
    # Use first few texts for robustness testing
    test_samples = test_texts[:3]
    
    for i, sample in enumerate(test_samples):
        text = sample['text']
        prompt = sample['prompt']
        original_rate = sample['survival_rate']
        
        print(f"  Testing sample {i+1}/{len(test_samples)}...")
        
        # Paraphrasing attack (simple word replacement)
        paraphrased = text.replace('found', 'discovered').replace('galaxy', 'universe').replace('explorer', 'adventurer')
        paraphrased_rate = lcs_watermark.detect_watermark(paraphrased, prompt)
        results['paraphrasing'].append({
            'original_rate': original_rate,
            'attacked_rate': paraphrased_rate,
            'retention': paraphrased_rate / original_rate if original_rate > 0 else 0
        })
        
        # Synonym replacement
        synonymed = text.replace('starship', 'spacecraft').replace('message', 'text').replace('screen', 'display')
        synonymed_rate = lcs_watermark.detect_watermark(synonymed, prompt)
        results['synonym_replacement'].append({
            'original_rate': original_rate,
            'attacked_rate': synonymed_rate,
            'retention': synonymed_rate / original_rate if original_rate > 0 else 0
        })
        
        # Text truncation (remove last 20%)
        truncated = text[:int(len(text) * 0.8)]
        truncated_rate = lcs_watermark.detect_watermark(truncated, prompt)
        results['text_truncation'].append({
            'original_rate': original_rate,
            'attacked_rate': truncated_rate,
            'retention': truncated_rate / original_rate if original_rate > 0 else 0
        })
        
        # Text addition (add prefix)
        addition = "Here is an interesting story: " + text
        addition_rate = lcs_watermark.detect_watermark(addition, prompt)
        results['text_addition'].append({
            'original_rate': original_rate,
            'attacked_rate': addition_rate,
            'retention': addition_rate / original_rate if original_rate > 0 else 0
        })
    
    # Calculate average retention rates
    for attack_type in list(results.keys()):  # Convert to list to avoid iteration issues
        attack_results = results[attack_type]
        if attack_results:
            avg_retention = np.mean([r['retention'] for r in attack_results])
            results[f'{attack_type}_avg_retention'] = avg_retention
            print(f"    {attack_type}: {avg_retention:.1%} retention")
    
    return results

def main():
    """Run comprehensive LCS evaluation."""
    print("="*70)
    print("         COMPREHENSIVE LCS EVALUATION")
    print("="*70)
    print(f"Evaluation started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Device: {'CUDA' if torch.cuda.is_available() else 'CPU'}")
    
    try:
        # Setup
        from watermark.auto_watermark import AutoWatermark
        from utils.transformers_config import TransformersConfig
        from transformers import AutoTokenizer
        
        # Load tokenizer
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        tokenizer = AutoTokenizer.from_pretrained("GSAI-ML/LLaDA-8B-Instruct", trust_remote_code=True)
        
        transformers_config = TransformersConfig(
            model=None,  # Will be loaded by LCS
            tokenizer=tokenizer,
            vocab_size=tokenizer.vocab_size,
            device=device
        )
        
        # Load LCS watermark
        print("\nLoading LCS watermarking system...")
        lcs_watermark = AutoWatermark.load("LCS", "config/LCS.json", transformers_config)
        print("✅ LCS loaded successfully!")
        
        # Prepare evaluation data
        evaluation_results = {
            'timestamp': datetime.now().isoformat(),
            'config': {
                'algorithm': 'LCS',
                'model': lcs_watermark.config.model_name,
                'strategy': lcs_watermark.config.seeding_strategy,
                'secret_key': lcs_watermark.config.secret_key,
                'detection_threshold': lcs_watermark.config.detection_threshold
            }
        }
        
        # 1. Basic Performance Evaluation
        print("\n1. Basic Performance Evaluation")
        print("-" * 40)
        prompts = load_evaluation_prompts()
        basic_results = evaluate_basic_performance(lcs_watermark, prompts)
        evaluation_results['basic_performance'] = basic_results
        
        print(f"\nBasic Performance Results:")
        print(f"  Successful generations: {basic_results['successful_generations']}/{basic_results['total_prompts']}")
        print(f"  Average survival rate: {basic_results['avg_survival_rate']:.1%} ± {basic_results['std_survival_rate']:.1%}")
        print(f"  Success rate (>50%): {basic_results['success_rate']:.1%}")
        print(f"  Range: {basic_results['min_survival_rate']:.1%} - {basic_results['max_survival_rate']:.1%}")
        
        # 2. Strategy Comparison
        print("\n2. Strategy Comparison")
        print("-" * 40)
        strategy_results = evaluate_strategy_comparison(transformers_config)
        evaluation_results['strategy_comparison'] = strategy_results
        
        for strategy, results in strategy_results.items():
            if 'error' not in results:
                print(f"  {strategy.capitalize()} strategy:")
                print(f"    Average survival: {results['avg_survival_rate']:.1%}")
                print(f"    Success rate: {results['success_rate']:.1%}")
        
        # 3. Robustness Evaluation
        if basic_results['texts']:
            print("\n3. Robustness Evaluation")
            print("-" * 40)
            robustness_results = evaluate_robustness(lcs_watermark, basic_results['texts'])
            evaluation_results['robustness'] = robustness_results
            
            print("Robustness against attacks:")
            for attack_type in ['paraphrasing', 'synonym_replacement', 'text_truncation', 'text_addition']:
                if f'{attack_type}_avg_retention' in robustness_results:
                    retention = robustness_results[f'{attack_type}_avg_retention']
                    status = "🔒 ROBUST" if retention > 0.7 else "⚠️ MODERATE" if retention > 0.4 else "💥 VULNERABLE"
                    print(f"  {attack_type.replace('_', ' ').title()}: {retention:.1%} {status}")
        
        # 4. Overall Assessment
        print("\n" + "="*70)
        print("                 OVERALL ASSESSMENT")
        print("="*70)
        
        overall_score = basic_results['avg_survival_rate']
        
        if overall_score >= 0.9:
            grade = "A+ (EXCELLENT)"
            assessment = "🏆 LCS watermarking is performing exceptionally well!"
        elif overall_score >= 0.8:
            grade = "A (VERY GOOD)"
            assessment = "🥇 LCS watermarking is performing very well!"
        elif overall_score >= 0.7:
            grade = "B+ (GOOD)"
            assessment = "✅ LCS watermarking is performing well!"
        elif overall_score >= 0.6:
            grade = "B (ACCEPTABLE)"
            assessment = "👍 LCS watermarking is performing acceptably."
        elif overall_score >= 0.5:
            grade = "C (NEEDS IMPROVEMENT)"
            assessment = "⚠️ LCS watermarking needs improvement."
        else:
            grade = "F (POOR)"
            assessment = "❌ LCS watermarking has significant issues."
        
        print(f"Overall Performance Score: {overall_score:.1%}")
        print(f"Grade: {grade}")
        print(f"Assessment: {assessment}")
        
        evaluation_results['overall_assessment'] = {
            'score': overall_score,
            'grade': grade,
            'assessment': assessment
        }
        
        # Save detailed results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"lcs_evaluation_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(evaluation_results, f, indent=2, ensure_ascii=False)
        
        print(f"\nDetailed results saved to: {filename}")
        print("="*70)
        
        return overall_score >= 0.6  # Consider success if score >= 60%
        
    except Exception as e:
        print(f"❌ Evaluation failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
