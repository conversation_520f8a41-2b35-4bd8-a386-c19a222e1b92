#!/usr/bin/env python3
"""
LCS Watermarking Demo for MarkLLM

This script demonstrates the Latent Constraint Seeding (LCS) watermarking
capabilities integrated into the MarkLLM framework.
"""

import sys
import os
import torch
from datetime import datetime

# Add MarkLLM to path
sys.path.append('/mnt/newhome/kasra/MarkLLM')

def main():
    """Run LCS watermarking demo."""
    print("="*70)
    print("         LATENT CONSTRAINT SEEDING (LCS) DEMO")
    print("="*70)
    print(f"Demo started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Device: {'CUDA' if torch.cuda.is_available() else 'CPU'}")
    
    try:
        # Import required modules
        from watermark.auto_watermark import AutoWatermark
        from utils.transformers_config import TransformersConfig
        from transformers import AutoTokenizer
        
        # Load tokenizer
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        tokenizer = AutoTokenizer.from_pretrained("GSAI-ML/LLaDA-8B-Instruct", trust_remote_code=True)
        
        # Setup transformers config
        transformers_config = TransformersConfig(
            model=None,  # Will be loaded by LCS
            tokenizer=tokenizer,
            vocab_size=tokenizer.vocab_size,
            device=device
        )
        
        # Load LCS watermarking
        print("\n1. Loading LCS Watermarking System...")
        lcs_watermark = AutoWatermark.load(
            algorithm_name="LCS",
            algorithm_config="config/LCS.json",
            transformers_config=transformers_config
        )
        print("✅ LCS watermarking system loaded successfully!")
        print(f"   Model: {lcs_watermark.config.model_name}")
        print(f"   Strategy: {lcs_watermark.config.seeding_strategy}")
        print(f"   Secret key: {lcs_watermark.config.secret_key}")
        
        # Test prompts
        prompts = [
            "In a galaxy far, far away, a lone explorer found a derelict starship. Inside, the only clue to its fate was a single, cryptic message on the main screen:",
            "Write a short story about a robot who discovers music for the first time.",
            "Explain the concept of artificial intelligence to a child using simple analogies.",
            "Describe a day in the life of a person living in a city on Mars in the year 2150."
        ]
        
        results = []
        
        for i, prompt in enumerate(prompts, 1):
            print(f"\n{i}. Processing Prompt {i}...")
            print(f"Prompt: {prompt}")
            
            # Generate watermarked text
            print("   Generating watermarked text...")
            watermarked_text = lcs_watermark.generate_watermarked_text(prompt)
            
            # Detect watermark
            print("   Detecting watermark...")
            survival_rate = lcs_watermark.detect_watermark(watermarked_text, prompt)
            
            # Get visualization data
            viz_data = lcs_watermark.get_data_for_visualization(watermarked_text, prompt)
            
            print(f"   ✅ Generation complete!")
            print(f"   Watermark survival rate: {survival_rate:.1%}")
            print(f"   Text length: {len(watermarked_text)} characters")
            print(f"   Highlighted tokens: {sum(viz_data.highlight_values)}")
            print(f"   Generated text: {watermarked_text[:150]}...")
            
            results.append({
                'prompt': prompt,
                'watermarked_text': watermarked_text,
                'survival_rate': survival_rate,
                'highlighted_tokens': sum(viz_data.highlight_values)
            })
        
        # Summary statistics
        print("\n" + "="*70)
        print("                    DEMO SUMMARY")
        print("="*70)
        
        survival_rates = [r['survival_rate'] for r in results]
        avg_survival = sum(survival_rates) / len(survival_rates)
        
        print(f"Total prompts processed: {len(results)}")
        print(f"Average survival rate: {avg_survival:.1%}")
        print(f"Success rate (>50% survival): {sum(1 for rate in survival_rates if rate > 0.5)}/{len(survival_rates)}")
        
        # Show individual results
        print("\nIndividual Results:")
        for i, result in enumerate(results, 1):
            status = "✅ SUCCESS" if result['survival_rate'] > 0.5 else "⚠️ PARTIAL" if result['survival_rate'] > 0.2 else "❌ FAILED"
            print(f"  {i}. Survival rate: {result['survival_rate']:6.1%} {status}")
        
        # Test robustness with paraphrasing
        print(f"\n{len(results)+1}. Testing Robustness Against Simple Paraphrasing...")
        
        test_text = results[0]['watermarked_text']
        test_prompt = results[0]['prompt']
        
        # Simple paraphrasing by word replacement
        paraphrased = test_text.replace('explorer', 'adventurer').replace('found', 'discovered').replace('starship', 'spacecraft')
        paraphrased_survival = lcs_watermark.detect_watermark(paraphrased, test_prompt)
        
        print(f"   Original survival rate: {results[0]['survival_rate']:.1%}")
        print(f"   Paraphrased survival rate: {paraphrased_survival:.1%}")
        
        if paraphrased_survival > 0.5:
            print("   ✅ LCS shows strong resistance to paraphrasing!")
        elif paraphrased_survival > 0.2:
            print("   ⚠️ LCS shows moderate resistance to paraphrasing.")
        else:
            print("   ❌ LCS is vulnerable to paraphrasing attacks.")
        
        # Final evaluation
        print("\n" + "="*70)
        print("                  FINAL EVALUATION")
        print("="*70)
        
        if avg_survival >= 0.8:
            print("🎉 EXCELLENT: LCS watermarking is working exceptionally well!")
        elif avg_survival >= 0.6:
            print("✅ GOOD: LCS watermarking is working well with minor issues.")
        elif avg_survival >= 0.4:
            print("⚠️ MODERATE: LCS watermarking is partially working.")
        else:
            print("❌ POOR: LCS watermarking needs significant improvement.")
        
        print(f"\nOverall Performance Score: {avg_survival:.1%}")
        print(f"Robustness Score: {paraphrased_survival:.1%}")
        
        # Save results
        import json
        demo_results = {
            'timestamp': datetime.now().isoformat(),
            'config': {
                'algorithm': 'LCS',
                'strategy': lcs_watermark.config.seeding_strategy,
                'secret_key': lcs_watermark.config.secret_key
            },
            'results': results,
            'summary': {
                'average_survival_rate': avg_survival,
                'paraphrased_survival_rate': paraphrased_survival,
                'total_prompts': len(results)
            }
        }
        
        with open('lcs_demo_results.json', 'w') as f:
            json.dump(demo_results, f, indent=2, ensure_ascii=False)
        
        print(f"\nDemo results saved to: lcs_demo_results.json")
        print("="*70)
        
    except Exception as e:
        print(f"❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
