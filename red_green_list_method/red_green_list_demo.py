#!/usr/bin/env python3
"""
Red-Green List Watermark Demo Script
Simple demo to test Red-Green List watermark functionality in MarkLLM framework.
"""

import torch
from watermark.auto_watermark import AutoWatermark
from utils.transformers_config import TransformersConfig
from transformers import AutoTokenizer

def demo_red_green_list():
    """Demo the Red-Green List watermark functionality."""
    
    print("Red-Green List Watermark Demo")
    print("=" * 40)
    
    # Device
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"Using device: {device}")

    # Setup
    print("Setting up Red-Green List watermark...")
    tokenizer = AutoTokenizer.from_pretrained('GSAI-ML/LLaDA-8B-Instruct', trust_remote_code=True)
    
    transformers_config = TransformersConfig(
        model=None,
        tokenizer=tokenizer,
        vocab_size=tokenizer.vocab_size,
        device=device,
        max_new_tokens=100,
        do_sample=True,
        min_length=30,
        no_repeat_ngram_size=4
    )

    # Load watermark
    my_watermark = AutoWatermark.load('RedGreenList', 
                                      algorithm_config='config/RedGreenList.json',
                                      transformers_config=transformers_config)
    print("✓ Red-Green List watermark loaded")
    
    # Test prompt
    test_prompt = "Once upon a time in a magical forest, there lived a wise old owl who"
    print(f"\nTest prompt: '{test_prompt}'")
    
    try:
        # Generate watermarked text
        print("\nGenerating watermarked text...")
        watermarked_text = my_watermark.generate_watermarked_text(test_prompt)
        print(f"Watermarked text: '{watermarked_text}'")
        
        # Generate unwatermarked text
        print("\nGenerating unwatermarked text...")
        unwatermarked_text = my_watermark.generate_unwatermarked_text(test_prompt)
        print(f"Unwatermarked text: '{unwatermarked_text}'")
        
        # Detect watermarks
        print("\nDetecting watermarks...")
        w_result = my_watermark.detect_watermark(watermarked_text, prompt=test_prompt)
        u_result = my_watermark.detect_watermark(unwatermarked_text, prompt=test_prompt)
        
        print(f"\nWatermarked text detection:")
        print(f"  Score: {w_result['score']:.4f}")
        print(f"  Is watermarked: {w_result['is_watermarked']}")
        print(f"  Threshold: {w_result['threshold']}")
        
        print(f"\nUnwatermarked text detection:")
        print(f"  Score: {u_result['score']:.4f}")
        print(f"  Is watermarked: {u_result['is_watermarked']}")
        print(f"  Threshold: {u_result['threshold']}")
        
        # Evaluation
        separation = w_result['score'] - u_result['score']
        print(f"\nSeparation: {separation:.4f}")
        
        if w_result['is_watermarked'] and not u_result['is_watermarked']:
            print("✅ Demo successful! Watermark correctly detected/not detected.")
        elif separation > 0.1:
            print("✅ Demo partially successful! Good separation between scores.")
        else:
            print("⚠️  Demo shows weak performance. Low separation between scores.")
            
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    demo_red_green_list()
