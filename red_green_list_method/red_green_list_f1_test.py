#!/usr/bin/env python3
"""
Red-Green List Watermark F1 Test Script
Tests the Red-Green List watermark using MarkLLM framework with F1 evaluation similar to the original red-green-test.py
"""

import torch
import numpy as np
from evaluation.dataset import C4Dataset
from watermark.auto_watermark import AutoWatermark
from utils.transformers_config import TransformersConfig
from transformers import AutoTokenizer
from sklearn.metrics import f1_score, precision_score, recall_score, classification_report

def run_red_green_list_f1_evaluation():
    """Run F1 evaluation for Red-Green List watermark using MarkLLM framework."""
    
    print("=" * 60)
    print("         RED-GREEN LIST F1 EVALUATION (MarkLLM)")
    print("=" * 60)
    
    # Device
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"Using device: {device}")

    # Setup
    print("Setting up Red-Green List watermark...")
    tokenizer = AutoTokenizer.from_pretrained('GSAI-ML/LLaDA-8B-Instruct', trust_remote_code=True)
    
    transformers_config = TransformersConfig(
        model=None,
        tokenizer=tokenizer,
        vocab_size=tokenizer.vocab_size,
        device=device,
        max_new_tokens=128,
        do_sample=True,
        min_length=50,
        no_repeat_ngram_size=4
    )

    # Load watermark
    my_watermark = AutoWatermark.load('RedGreenList', 
                                      algorithm_config='config/RedGreenList.json',
                                      transformers_config=transformers_config)
    print("✓ Red-Green List watermark loaded")
    
    # Test prompts for F1 evaluation
    test_prompts = [
        "In a galaxy far, far away, a lone explorer found a derelict starship. Inside, the only clue to its fate was a single, cryptic message on the main screen:",
        "The ancient library held secrets that had been forgotten for centuries. As the dust settled on the old tome, the scholar discovered:",
        "Deep in the Amazon rainforest, the research team stumbled upon something extraordinary. The discovery would change everything they knew about:",
        "The detective examined the crime scene carefully. After hours of investigation, one crucial piece of evidence revealed:",
        "On the space station orbiting Mars, the astronaut received an unexpected transmission. The message contained coordinates to:",
        "The AI researcher was on the verge of a breakthrough. Years of work had led to this moment when the algorithm finally:",
        "In the medieval castle, the archaeologist found a hidden chamber. Behind the stone wall lay a manuscript that described:",
        "The quantum physicist stared at the experimental results in disbelief. The data suggested something that challenged the very foundation of:",
        "The marine biologist descended into the deepest ocean trench. What they found at the bottom was unlike anything previously documented:",
        "The time traveler arrived in the year 3021, only to discover that humanity had evolved in ways that were both fascinating and:"
    ]
    
    print(f"Testing with {len(test_prompts)} prompts, generating both watermarked and non-watermarked texts...")
    
    # Store results for F1 calculation
    all_scores = []
    all_labels = []  # 1 for watermarked, 0 for non-watermarked
    
    watermarked_scores = []
    normal_scores = []
    
    for i, prompt_text in enumerate(test_prompts):
        print(f"\nProcessing prompt {i+1}/{len(test_prompts)}...")
        
        try:
            # Generate watermarked text
            watermarked_text = my_watermark.generate_watermarked_text(prompt_text)
            
            # Generate non-watermarked text
            normal_text = my_watermark.generate_unwatermarked_text(prompt_text)
            
            # Detect watermarks
            watermarked_result = my_watermark.detect_watermark(watermarked_text, prompt=prompt_text)
            normal_result = my_watermark.detect_watermark(normal_text, prompt=prompt_text)
            
            watermarked_score = watermarked_result['score']
            normal_score = normal_result['score']
            
            # Store scores and labels
            all_scores.extend([watermarked_score, normal_score])
            all_labels.extend([1, 0])  # 1 for watermarked, 0 for non-watermarked
            
            watermarked_scores.append(watermarked_score)
            normal_scores.append(normal_score)
            
            print(f"  Watermarked score: {watermarked_score:.3f}")
            print(f"  Normal score: {normal_score:.3f}")
            
        except Exception as e:
            print(f"  ❌ Error processing prompt {i+1}: {e}")
            continue
    
    if not watermarked_scores or not normal_scores:
        print("❌ No valid results obtained. Evaluation failed.")
        return None
    
    # Calculate threshold (mean of watermarked and normal scores)
    threshold = (np.mean(watermarked_scores) + np.mean(normal_scores)) / 2
    print(f"\nOptimal threshold: {threshold:.3f}")
    
    # Convert scores to binary predictions
    predictions = [1 if score > threshold else 0 for score in all_scores]
    
    # Calculate metrics
    f1 = f1_score(all_labels, predictions)
    precision = precision_score(all_labels, predictions)
    recall = recall_score(all_labels, predictions)
    
    print("\n" + "="*60)
    print("           RED-GREEN LIST F1 EVALUATION RESULTS")
    print("="*60)
    print(f"Threshold used: {threshold:.3f}")
    print(f"F1 Score: {f1:.3f}")
    print(f"Precision: {precision:.3f}")
    print(f"Recall: {recall:.3f}")
    
    print(f"\nAverage watermarked detection score: {np.mean(watermarked_scores):.3f} (±{np.std(watermarked_scores):.3f})")
    print(f"Average normal detection score: {np.mean(normal_scores):.3f} (±{np.std(normal_scores):.3f})")
    
    # Detailed classification report
    print(f"\nDetailed Classification Report:")
    print(classification_report(all_labels, predictions, target_names=['Non-Watermarked', 'Watermarked']))
    
    # Success criteria
    if f1 > 0.8:
        print("\n✅ Excellent: F1 > 0.8 - Red-Green List watermark performs very well!")
    elif f1 > 0.6:
        print("\n✅ Good: F1 > 0.6 - Red-Green List watermark performs reasonably well.")
    elif f1 > 0.4:
        print("\n⚠️  Moderate: F1 > 0.4 - Red-Green List watermark has moderate performance.")
    else:
        print("\n❌ Poor: F1 ≤ 0.4 - Red-Green List watermark performance is poor.")
    
    return {
        'f1_score': f1,
        'precision': precision,
        'recall': recall,
        'threshold': threshold,
        'watermarked_mean': np.mean(watermarked_scores),
        'normal_mean': np.mean(normal_scores),
        'separation': np.mean(watermarked_scores) - np.mean(normal_scores)
    }

if __name__ == "__main__":
    try:
        results = run_red_green_list_f1_evaluation()
        if results:
            print(f"\n🎉 F1 Evaluation completed successfully!")
        else:
            print(f"\n❌ F1 Evaluation failed.")
    except Exception as e:
        print(f"\n❌ Error during F1 evaluation: {e}")
        import traceback
        traceback.print_exc()
